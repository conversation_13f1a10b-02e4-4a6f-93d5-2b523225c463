# Messenger Backend

Полноценный бекенд мессенджера на основе eJabberd (XMPP), LiveKit (звонки) и FastAPI.

## Архитектура

- **eJabberd** - XMPP сервер для обмена сообщениями и управления пользователями
- **LiveKit** - Сервер для аудио/видео звонков
- **FastAPI** - REST API для дополнительной функциональности
- **PostgreSQL** - База данных для хранения метаданных
- **Redis** - Кэширование и управление сессиями
- **MinIO** - S3-совместимое хранилище файлов

## Возможности

### ✅ Реализовано
- Регистрация и авторизация пользователей (JWT токены)
- Отправка и получение сообщений через XMPP
- Загрузка и скачивание файлов (S3)
- Создание групповых чатов
- Аудио/видео звонки через LiveKit
- История сообщений (MAM)
- Управление участниками групп
- Поиск пользователей
- Статусы пользователей (онлайн/офлайн)

### 🔄 В разработке
- Push уведомления
- Шифрование сообщений
- Модерация контента

## Быстрый старт

### Предварительные требования
- Docker и Docker Compose
- Python 3.12+ (для разработки)

### Запуск

1. Клонируйте репозиторий:
```bash
git clone <repository-url>
cd ejabberd_fastapi1
```

2. Запустите все сервисы:
```bash
docker compose up -d
```

3. Дождитесь запуска всех контейнеров (может занять 1-2 минуты)

4. Проверьте статус:
```bash
docker compose ps
```

### Проверка работоспособности

1. Откройте документацию API: http://localhost:8000/docs
2. Админка eJabberd: http://localhost:5280/admin (admin/admin123)
3. MinIO консоль: http://localhost:9001 (minioadmin/minioadmin123)

### Тестирование API

Запустите тесты:
```bash
cd backend
python test_api.py
```

## API Endpoints

### Аутентификация
- `POST /api/auth/register` - Регистрация пользователя
- `POST /api/auth/login` - Авторизация

### Пользователи
- `GET /api/users/profile/{username}` - Получение профиля
- `GET /api/users/search?q={query}` - Поиск пользователей
- `GET /api/users/online` - Список онлайн пользователей
- `POST /api/users/status` - Обновление статуса

### Сообщения
- `POST /api/messages/send` - Отправка сообщения
- `GET /api/messages/history/{jid}` - История сообщений

### Группы
- `POST /api/groups/create` - Создание группы
- `GET /api/groups/list` - Список групп пользователя
- `GET /api/groups/{group_id}/members` - Участники группы
- `POST /api/groups/{group_id}/invite` - Приглашение в группу
- `DELETE /api/groups/{group_id}/members/{user_jid}` - Удаление из группы

### Файлы
- `POST /api/files/upload` - Загрузка файла
- `GET /api/files/{file_id}` - Получение файла

### Звонки
- `POST /api/calls/create` - Создание звонка
- `GET /api/calls/{call_id}` - Информация о звонке
- `POST /api/calls/{call_id}/end` - Завершение звонка

## Конфигурация

### Переменные окружения

Основные настройки задаются в `docker-compose.yml`:

- `DATABASE_URL` - Подключение к PostgreSQL
- `REDIS_URL` - Подключение к Redis
- `MINIO_*` - Настройки S3 хранилища
- `EJABBERD_*` - Настройки eJabberd
- `LIVEKIT_*` - Настройки LiveKit

### Порты

- `8000` - FastAPI backend
- `5222` - XMPP клиентские соединения
- `5280` - eJabberd HTTP API и админка
- `7880` - LiveKit RTC
- `5432` - PostgreSQL
- `6379` - Redis
- `9000/9001` - MinIO

## Разработка

### Локальная разработка backend

1. Запустите инфраструктуру:
```bash
docker compose up -d postgres redis minio ejabberd livekit
```

2. Установите зависимости:
```bash
cd backend
uv sync
```

3. Запустите FastAPI в режиме разработки:
```bash
uv run app.py
```

### Структура проекта

```
├── backend/
│   ├── app.py           # Основной файл FastAPI
│   ├── Dockerfile       # Docker образ
│   └── test_api.py      # Тесты API
├── ejabberd/
│   ├── ejabberd.yml     # Конфигурация eJabberd
│   └── database.yml     # Настройки БД для eJabberd
├── livekit/
│   └── livekit.yaml     # Конфигурация LiveKit
├── sql/
│   └── init.sql         # Инициализация БД
└── docker-compose.yml   # Оркестрация сервисов
```

## XMPP клиенты

Для подключения XMPP клиентов используйте:
- Сервер: `localhost:5222`
- Домен: `localhost`
- Пользователи создаются через API или админку

Рекомендуемые клиенты:
- **Desktop**: Gajim, Psi+, Conversations
- **Mobile**: Conversations (Android), ChatSecure (iOS)
- **Web**: Converse.js, JSXC

## Мониторинг

### Логи
```bash
# Все сервисы
docker-compose logs -f

# Конкретный сервис
docker-compose logs -f fastapi_backend
docker-compose logs -f ejabberd
```

### Метрики
- eJabberd статистика: http://localhost:5280/admin
- LiveKit dashboard: встроенный в LiveKit

## Безопасность

⚠️ **Важно для продакшена:**

1. Смените все пароли по умолчанию
2. Используйте HTTPS/TLS
3. Настройте firewall
4. Обновите `SECRET_KEY` для JWT
5. Включите SSL для PostgreSQL
6. Настройте backup

## Лицензия

MIT License
