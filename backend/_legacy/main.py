from fastapi import <PERSON>AP<PERSON>, HTTPException, Depends, UploadFile, File
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel
from typing import Optional, List
import json
import os
import uuid
from datetime import datetime, timedelta
import aiohttp
import asyncpg
import aioredis
from minio import Minio
from minio.error import S3Error
from livekit import api
from jose import JWTError, jwt
from passlib.context import CryptContext
from dotenv import load_dotenv

# Загружаем переменные окружения из .env файла
load_dotenv()

# Конфигурация
DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://postgres:postgres123@localhost:5432/messenger")
REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379")
MINIO_ENDPOINT = os.getenv("MINIO_ENDPOINT", "localhost:9000")
MINIO_ACCESS_KEY = os.getenv("MINIO_ACCESS_KEY", "minioadmin")
MINIO_SECRET_KEY = os.getenv("MINIO_SECRET_KEY", "minioadmin123")
EJABBERD_API_URL = os.getenv("EJABBERD_API_URL", "http://localhost:5280/api")
EJABBERD_ADMIN_USER = os.getenv("EJABBERD_ADMIN_USER", "admin")
EJABBERD_ADMIN_PASSWORD = os.getenv("EJABBERD_ADMIN_PASSWORD", "admin123")
LIVEKIT_API_KEY = os.getenv("LIVEKIT_API_KEY", "APIKey")
LIVEKIT_API_SECRET = os.getenv("LIVEKIT_API_SECRET", "secret123")
LIVEKIT_URL = os.getenv("LIVEKIT_URL", "ws://localhost:7880")

# JWT конфигурация
SECRET_KEY = os.getenv("SECRET_KEY", "your-super-secret-key-change-in-production")
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

# Контекст для хеширования паролей
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

app = FastAPI(title="Messenger Backend", version="1.0.0")

# CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Глобальные соединения
db_pool = None
redis = None
s3_client = None
livekit_api = None

# Схемы данных
class UserCreate(BaseModel):
    username: str
    password: str
    display_name: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None

class UserProfile(BaseModel):
    username: str
    display_name: Optional[str] = None
    avatar_url: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    status: str
    last_seen: datetime

class GroupChatCreate(BaseModel):
    name: str
    description: Optional[str] = None
    is_public: bool = True
    members: List[str] = []  # список JID

class CallCreate(BaseModel):
    participants: List[str]  # список JID
    call_type: str = "video"  # audio или video

class MessageSend(BaseModel):
    to_jid: str
    body: str
    message_type: str = "chat"  # chat или groupchat

class UserLogin(BaseModel):
    username: str
    password: str

class GroupInvite(BaseModel):
    user_jids: List[str]

class UserStatusUpdate(BaseModel):
    status: str

# Инициализация соединений
@app.on_event("startup")
async def startup():
    global db_pool, redis, s3_client, livekit_api
    
    # PostgreSQL
    db_pool = await asyncpg.create_pool(DATABASE_URL, min_size=5, max_size=20)
    
    # Redis
    redis = await aioredis.from_url(REDIS_URL)
    
    # MinIO S3
    s3_client = Minio(
        MINIO_ENDPOINT,
        access_key=MINIO_ACCESS_KEY,
        secret_key=MINIO_SECRET_KEY,
        secure=False
    )
    
    # Создаем bucket если не существует
    try:
        if not s3_client.bucket_exists("messenger-files"):
            s3_client.make_bucket("messenger-files")
    except S3Error as e:
        print(f"MinIO error: {e}")
    
    # LiveKit API
    livekit_api = api.LiveKitAPI(
        url=LIVEKIT_URL,
        api_key=LIVEKIT_API_KEY,
        api_secret=LIVEKIT_API_SECRET
    )

@app.on_event("shutdown")
async def shutdown():
    if db_pool:
        await db_pool.close()
    if redis:
        await redis.close()

# Хелперы
async def get_db():
    async with db_pool.acquire() as conn:
        yield conn

async def call_ejabberd_api(command: str, data: dict = None):
    """Вызов API eJabberd"""
    url = f"{EJABBERD_API_URL}/{command}"
    auth = aiohttp.BasicAuth(EJABBERD_ADMIN_USER, EJABBERD_ADMIN_PASSWORD)
    
    async with aiohttp.ClientSession() as session:
        async with session.post(url, json=data or {}, auth=auth) as resp:
            if resp.status == 200:
                return await resp.json()
            else:
                text = await resp.text()
                raise HTTPException(status_code=resp.status, detail=f"eJabberd API error: {text}")

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """Создание JWT токена"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def verify_password(plain_password, hashed_password):
    """Проверка пароля"""
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password):
    """Хеширование пароля"""
    return pwd_context.hash(password)

security = HTTPBearer()

async def verify_token(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Проверка JWT токена"""
    credentials_exception = HTTPException(
        status_code=401,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(credentials.credentials, SECRET_KEY, algorithms=[ALGORITHM])
        user_jid: str = payload.get("sub")
        if user_jid is None:
            raise credentials_exception
        return {"user_jid": user_jid}
    except JWTError:
        raise credentials_exception

# API Endpoints

@app.post("/api/auth/register")
async def register_user(user_data: UserCreate):
    """Регистрация нового пользователя"""
    try:
        # Создаем пользователя в eJabberd
        await call_ejabberd_api("register", {
            "user": user_data.username,
            "host": "localhost",
            "password": user_data.password
        })
        
        # Сохраняем дополнительную информацию в нашей БД
        async with db_pool.acquire() as conn:
            await conn.execute("""
                INSERT INTO users_extended (username, display_name, email, phone)
                VALUES ($1, $2, $3, $4)
                ON CONFLICT (username, server) DO UPDATE SET
                display_name = $2, email = $3, phone = $4
            """, user_data.username, user_data.display_name, user_data.email, user_data.phone)
        
        return {"success": True, "message": "User registered successfully"}
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@app.post("/api/auth/login")
async def login_user(user_data: UserLogin):
    """Авторизация пользователя"""
    try:
        # Проверяем пароль через eJabberd
        result = await call_ejabberd_api("check_password", {
            "user": user_data.username,
            "host": "localhost",
            "password": user_data.password
        })

        if result.get("res") == 0:  # успешно
            user_jid = f"{user_data.username}@localhost"
            access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
            access_token = create_access_token(
                data={"sub": user_jid}, expires_delta=access_token_expires
            )

            # Обновляем статус в Redis
            await redis.hset(f"user:{user_jid}", "status", "online")

            return {
                "success": True,
                "access_token": access_token,
                "token_type": "bearer",
                "user_jid": user_jid
            }
        else:
            raise HTTPException(status_code=401, detail="Invalid credentials")
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@app.get("/api/users/profile/{username}")
async def get_user_profile(username: str, current_user = Depends(verify_token)):
    """Получение профиля пользователя"""
    async with db_pool.acquire() as conn:
        user = await conn.fetchrow("""
            SELECT * FROM users_extended WHERE username = $1
        """, username)
        
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        return UserProfile(**dict(user))

@app.post("/api/files/upload")
async def upload_file(file: UploadFile = File(...), current_user = Depends(verify_token)):
    """Загрузка файла в S3"""
    try:
        file_id = str(uuid.uuid4())
        file_ext = os.path.splitext(file.filename)[1]
        s3_key = f"files/{file_id}{file_ext}"
        
        # Загружаем в S3
        s3_client.put_object(
            "messenger-files",
            s3_key,
            file.file,
            length=file.size,
            content_type=file.content_type
        )
        
        # Сохраняем метаданные в БД
        async with db_pool.acquire() as conn:
            file_record = await conn.fetchrow("""
                INSERT INTO files (filename, original_name, content_type, size_bytes, s3_key, uploaded_by)
                VALUES ($1, $2, $3, $4, $5, $6)
                RETURNING id, s3_key
            """, f"{file_id}{file_ext}", file.filename, file.content_type, 
                file.size, s3_key, current_user["user_jid"])
        
        return {
            "file_id": str(file_record["id"]),
            "url": f"http://localhost:8000/api/files/{file_record['id']}"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/files/{file_id}")
async def get_file(file_id: str):
    """Получение файла"""
    async with db_pool.acquire() as conn:
        file_record = await conn.fetchrow("""
            SELECT * FROM files WHERE id = $1
        """, file_id)
        
        if not file_record:
            raise HTTPException(status_code=404, detail="File not found")
        
        # Генерируем presigned URL
        url = s3_client.presigned_get_object(
            "messenger-files", 
            file_record["s3_key"], 
            expires=timedelta(hours=1)
        )
        
        return {"url": url}

@app.post("/api/groups/create")
async def create_group_chat(group_data: GroupChatCreate, current_user = Depends(verify_token)):
    """Создание группового чата"""
    try:
        room_id = str(uuid.uuid4())
        room_jid = f"{room_id}@conference.localhost"
        
        # Создаем комнату в eJabberd
        await call_ejabberd_api("create_room", {
            "name": room_id,
            "service": "conference.localhost",
            "host": "localhost"
        })
        
        # Устанавливаем конфигурацию комнаты
        await call_ejabberd_api("change_room_option", {
            "name": room_id,
            "service": "conference.localhost",
            "option": "title",
            "value": group_data.name
        })
        
        # Сохраняем в БД
        async with db_pool.acquire() as conn:
            group_record = await conn.fetchrow("""
                INSERT INTO group_chats (jid, name, description, created_by, is_public)
                VALUES ($1, $2, $3, $4, $5)
                RETURNING id
            """, room_jid, group_data.name, group_data.description, 
                current_user["user_jid"], group_data.is_public)
            
            # Добавляем создателя как администратора
            await conn.execute("""
                INSERT INTO group_members (group_id, user_jid, role)
                VALUES ($1, $2, 'admin')
            """, group_record["id"], current_user["user_jid"])
            
            # Добавляем других участников
            for member_jid in group_data.members:
                await conn.execute("""
                    INSERT INTO group_members (group_id, user_jid)
                    VALUES ($1, $2)
                    ON CONFLICT (group_id, user_jid) DO NOTHING
                """, group_record["id"], member_jid)
        
        return {
            "success": True,
            "group_id": str(group_record["id"]),
            "jid": room_jid
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/calls/create")
async def create_call(call_data: CallCreate, current_user = Depends(verify_token)):
    """Создание звонка через LiveKit"""
    try:
        room_name = f"call-{uuid.uuid4()}"
        
        # Комната будет создана автоматически при подключении первого участника
        
        # Записываем в БД
        async with db_pool.acquire() as conn:
            call_record = await conn.fetchrow("""
                INSERT INTO calls (livekit_room_id, call_type, initiator_jid, participants, status)
                VALUES ($1, $2, $3, $4, 'initiated')
                RETURNING id
            """, room_name, call_data.call_type, current_user["user_jid"], 
                json.dumps(call_data.participants))
        
        # Генерируем токены для участников
        tokens = {}
        for participant_jid in [current_user["user_jid"]] + call_data.participants:
            username = participant_jid.split('@')[0]
            token = api.AccessToken(LIVEKIT_API_KEY, LIVEKIT_API_SECRET)\
                .with_identity(username)\
                .with_name(username)\
                .with_grants(api.VideoGrants(room_join=True, room=room_name))
            
            tokens[participant_jid] = token.to_jwt()
        
        return {
            "call_id": str(call_record["id"]),
            "room_name": room_name,
            "livekit_url": LIVEKIT_URL,
            "tokens": tokens
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/calls/{call_id}")
async def get_call_info(call_id: str, current_user = Depends(verify_token)):
    """Получение информации о звонке"""
    async with db_pool.acquire() as conn:
        call = await conn.fetchrow("""
            SELECT * FROM calls WHERE id = $1
        """, call_id)

        if not call:
            raise HTTPException(status_code=404, detail="Call not found")

        return dict(call)

@app.post("/api/calls/{call_id}/end")
async def end_call(call_id: str, current_user = Depends(verify_token)):
    """Завершение звонка"""
    async with db_pool.acquire() as conn:
        call = await conn.fetchrow("""
            SELECT * FROM calls WHERE id = $1
        """, call_id)

        if not call:
            raise HTTPException(status_code=404, detail="Call not found")

        # Обновляем статус звонка
        await conn.execute("""
            UPDATE calls
            SET status = 'ended', ended_at = NOW(),
                duration_seconds = EXTRACT(EPOCH FROM (NOW() - started_at))
            WHERE id = $1
        """, call_id)

        return {"success": True, "message": "Call ended"}

@app.post("/api/messages/send")
async def send_message(message_data: MessageSend, current_user = Depends(verify_token)):
    """Отправка сообщения через eJabberd"""
    try:
        # Отправляем сообщение через eJabberd API
        await call_ejabberd_api("send_message", {
            "type": message_data.message_type,
            "from": current_user["user_jid"],
            "to": message_data.to_jid,
            "subject": "",
            "body": message_data.body
        })

        return {"success": True, "message": "Message sent"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/messages/history/{jid}")
async def get_message_history(
    jid: str,
    limit: int = 50,
    offset: int = 0,
    current_user = Depends(verify_token)
):
    """Получение истории сообщений через eJabberd MAM"""
    try:
        # Получаем историю через eJabberd MAM API
        result = await call_ejabberd_api("get_mam_messages", {
            "user": current_user["user_jid"].split('@')[0],
            "server": "localhost",
            "with": jid,
            "limit": limit,
            "offset": offset
        })

        return {"messages": result.get("messages", [])}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/groups/list")
async def list_user_groups(current_user = Depends(verify_token)):
    """Получение списка групп пользователя"""
    async with db_pool.acquire() as conn:
        groups = await conn.fetch("""
            SELECT gc.*, gm.role, gm.joined_at
            FROM group_chats gc
            JOIN group_members gm ON gc.id = gm.group_id
            WHERE gm.user_jid = $1
            ORDER BY gc.created_at DESC
        """, current_user["user_jid"])

        return {"groups": [dict(group) for group in groups]}

@app.get("/api/groups/{group_id}/members")
async def get_group_members(group_id: str, current_user = Depends(verify_token)):
    """Получение участников группы"""
    async with db_pool.acquire() as conn:
        # Проверяем, что пользователь является участником группы
        membership = await conn.fetchrow("""
            SELECT * FROM group_members
            WHERE group_id = $1 AND user_jid = $2
        """, group_id, current_user["user_jid"])

        if not membership:
            raise HTTPException(status_code=403, detail="Not a group member")

        members = await conn.fetch("""
            SELECT gm.*, ue.display_name, ue.avatar_url, ue.status
            FROM group_members gm
            LEFT JOIN users_extended ue ON gm.user_jid = CONCAT(ue.username, '@', ue.server)
            WHERE gm.group_id = $1
            ORDER BY gm.joined_at
        """, group_id)

        return {"members": [dict(member) for member in members]}

@app.post("/api/groups/{group_id}/invite")
async def invite_to_group(
    group_id: str,
    invite_data: GroupInvite,
    current_user = Depends(verify_token)
):
    """Приглашение пользователей в группу"""
    async with db_pool.acquire() as conn:
        # Проверяем права (только админы и модераторы могут приглашать)
        membership = await conn.fetchrow("""
            SELECT * FROM group_members
            WHERE group_id = $1 AND user_jid = $2 AND role IN ('admin', 'moderator')
        """, group_id, current_user["user_jid"])

        if not membership:
            raise HTTPException(status_code=403, detail="Insufficient permissions")

        # Добавляем новых участников
        for user_jid in invite_data.user_jids:
            await conn.execute("""
                INSERT INTO group_members (group_id, user_jid)
                VALUES ($1, $2)
                ON CONFLICT (group_id, user_jid) DO NOTHING
            """, group_id, user_jid)

        return {"success": True, "message": f"Invited {len(invite_data.user_jids)} users"}

@app.delete("/api/groups/{group_id}/members/{user_jid}")
async def remove_from_group(
    group_id: str,
    user_jid: str,
    current_user = Depends(verify_token)
):
    """Удаление пользователя из группы"""
    async with db_pool.acquire() as conn:
        # Проверяем права
        membership = await conn.fetchrow("""
            SELECT * FROM group_members
            WHERE group_id = $1 AND user_jid = $2 AND role IN ('admin', 'moderator')
        """, group_id, current_user["user_jid"])

        if not membership:
            raise HTTPException(status_code=403, detail="Insufficient permissions")

        # Удаляем участника
        await conn.execute("""
            DELETE FROM group_members
            WHERE group_id = $1 AND user_jid = $2
        """, group_id, user_jid)

        return {"success": True, "message": "User removed from group"}

# Webhook для LiveKit
@app.post("/webhooks/livekit")
async def livekit_webhook(request_data: dict):
    """Webhook для обработки событий LiveKit"""
    try:
        event_type = request_data.get("event")
        room_name = request_data.get("room", {}).get("name")

        if not room_name:
            return {"success": True}

        async with db_pool.acquire() as conn:
            if event_type == "room_started":
                # Обновляем статус звонка на "ongoing"
                await conn.execute("""
                    UPDATE calls
                    SET status = 'ongoing', started_at = NOW()
                    WHERE livekit_room_id = $1
                """, room_name)

            elif event_type == "room_finished":
                # Обновляем статус звонка на "ended"
                await conn.execute("""
                    UPDATE calls
                    SET status = 'ended', ended_at = NOW(),
                        duration_seconds = EXTRACT(EPOCH FROM (NOW() - started_at))
                    WHERE livekit_room_id = $1
                """, room_name)

        return {"success": True}
    except Exception as e:
        print(f"LiveKit webhook error: {e}")
        return {"success": False, "error": str(e)}

@app.get("/api/users/search")
async def search_users(q: str, limit: int = 10, current_user = Depends(verify_token)):
    """Поиск пользователей"""
    async with db_pool.acquire() as conn:
        users = await conn.fetch("""
            SELECT username, display_name, avatar_url, status
            FROM users_extended
            WHERE username ILIKE $1 OR display_name ILIKE $1
            LIMIT $2
        """, f"%{q}%", limit)

        return {"users": [dict(user) for user in users]}

@app.get("/api/users/online")
async def get_online_users(current_user = Depends(verify_token)):
    """Получение списка онлайн пользователей"""
    try:
        # Получаем онлайн пользователей из Redis
        online_users = []
        async for key in redis.scan_iter(match="user:*"):
            user_data = await redis.hgetall(key)
            if user_data.get("status") == "online":
                user_jid = key.decode().replace("user:", "")
                username = user_jid.split('@')[0]
                online_users.append({
                    "user_jid": user_jid,
                    "username": username,
                    "status": "online"
                })

        return {"online_users": online_users}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/users/status")
async def update_user_status(status_data: UserStatusUpdate, current_user = Depends(verify_token)):
    """Обновление статуса пользователя"""
    try:
        # Обновляем в Redis
        await redis.hset(f"user:{current_user['user_jid']}", "status", status_data.status)

        # Обновляем в БД
        async with db_pool.acquire() as conn:
            username = current_user["user_jid"].split('@')[0]
            await conn.execute("""
                UPDATE users_extended
                SET status = $1, last_seen = NOW()
                WHERE username = $2
            """, status_data.status, username)

        return {"success": True, "status": status_data.status}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Дополнительные эндпойнты

@app.get("/api/health")
async def health_check():
    """Проверка состояния сервиса"""
    try:
        # Проверяем подключение к БД
        async with db_pool.acquire() as conn:
            await conn.fetchval("SELECT 1")

        # Проверяем Redis
        await redis.ping()

        return {
            "status": "healthy",
            "services": {
                "database": "ok",
                "redis": "ok",
                "s3": "ok"
            }
        }
    except Exception as e:
        raise HTTPException(status_code=503, detail=f"Service unhealthy: {str(e)}")

@app.get("/api/stats")
async def get_stats(current_user = Depends(verify_token)):
    """Получение статистики"""
    async with db_pool.acquire() as conn:
        # Общая статистика
        total_users = await conn.fetchval("SELECT COUNT(*) FROM users_extended")
        total_groups = await conn.fetchval("SELECT COUNT(*) FROM group_chats")
        total_files = await conn.fetchval("SELECT COUNT(*) FROM files")
        total_calls = await conn.fetchval("SELECT COUNT(*) FROM calls")

        # Статистика пользователя
        user_groups = await conn.fetchval("""
            SELECT COUNT(*) FROM group_members
            WHERE user_jid = $1
        """, current_user["user_jid"])

        user_files = await conn.fetchval("""
            SELECT COUNT(*) FROM files
            WHERE uploaded_by = $1
        """, current_user["user_jid"])

        return {
            "global": {
                "total_users": total_users,
                "total_groups": total_groups,
                "total_files": total_files,
                "total_calls": total_calls
            },
            "user": {
                "groups": user_groups,
                "files": user_files
            }
        }

@app.post("/api/users/profile/update")
async def update_profile(profile_data: dict, current_user = Depends(verify_token)):
    """Обновление профиля пользователя"""
    try:
        async with db_pool.acquire() as conn:
            username = current_user["user_jid"].split('@')[0]

            # Обновляем только разрешенные поля
            allowed_fields = ['display_name', 'email', 'phone']
            update_fields = []
            values = []
            param_count = 1

            for field in allowed_fields:
                if field in profile_data:
                    update_fields.append(f"{field} = ${param_count}")
                    values.append(profile_data[field])
                    param_count += 1

            if update_fields:
                query = f"""
                    UPDATE users_extended
                    SET {', '.join(update_fields)}
                    WHERE username = ${param_count}
                """
                values.append(username)
                await conn.execute(query, *values)

            return {"success": True, "message": "Profile updated"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/groups/{group_id}/info")
async def get_group_info(group_id: str, current_user = Depends(verify_token)):
    """Получение информации о группе"""
    async with db_pool.acquire() as conn:
        # Проверяем, что пользователь является участником группы
        membership = await conn.fetchrow("""
            SELECT * FROM group_members
            WHERE group_id = $1 AND user_jid = $2
        """, group_id, current_user["user_jid"])

        if not membership:
            raise HTTPException(status_code=403, detail="Not a group member")

        # Получаем информацию о группе
        group_info = await conn.fetchrow("""
            SELECT * FROM group_chats WHERE id = $1
        """, group_id)

        if not group_info:
            raise HTTPException(status_code=404, detail="Group not found")

        # Считаем участников
        member_count = await conn.fetchval("""
            SELECT COUNT(*) FROM group_members WHERE group_id = $1
        """, group_id)

        result = dict(group_info)
        result["member_count"] = member_count
        result["user_role"] = membership["role"]

        return result

@app.post("/api/notifications/mark-read")
async def mark_notifications_read(notification_ids: List[str], current_user = Depends(verify_token)):
    """Отметка уведомлений как прочитанных"""
    async with db_pool.acquire() as conn:
        await conn.execute("""
            UPDATE notifications
            SET is_read = true
            WHERE id = ANY($1) AND user_jid = $2
        """, notification_ids, current_user["user_jid"])

        return {"success": True, "message": f"Marked {len(notification_ids)} notifications as read"}

@app.get("/api/notifications")
async def get_notifications(
    limit: int = 20,
    offset: int = 0,
    unread_only: bool = False,
    current_user = Depends(verify_token)
):
    """Получение уведомлений пользователя"""
    async with db_pool.acquire() as conn:
        where_clause = "WHERE user_jid = $1"
        params = [current_user["user_jid"]]

        if unread_only:
            where_clause += " AND is_read = false"

        notifications = await conn.fetch(f"""
            SELECT * FROM notifications
            {where_clause}
            ORDER BY created_at DESC
            LIMIT $2 OFFSET $3
        """, *params, limit, offset)

        return {"notifications": [dict(notif) for notif in notifications]}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)