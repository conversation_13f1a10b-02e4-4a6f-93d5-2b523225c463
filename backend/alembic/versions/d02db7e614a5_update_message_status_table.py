"""Update message status table

Revision ID: d02db7e614a5
Revises: 30111875268c
Create Date: 2025-09-04 19:22:27.060738

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'd02db7e614a5'
down_revision: Union[str, None] = '30111875268c'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Проверяем, существует ли старая таблица message_status
    op.execute("""
        DO $$
        BEGIN
            IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'message_status') THEN
                -- Переименовываем старую таблицу
                ALTER TABLE message_status RENAME TO message_status_old;
            END IF;
        END
        $$;
    """)

    # Создаем новую таблицу message_statuses с правильной структурой
    op.execute("""
        CREATE TABLE IF NOT EXISTS message_statuses (
            id UUID DEFAULT uuid_generate_v4() NOT NULL,
            message_id VARCHAR NOT NULL,
            user_jid VARCHAR NOT NULL,
            status VARCHAR NOT NULL DEFAULT 'sent',
            timestamp TIMESTAMP WITH TIME ZONE DEFAULT now(),
            PRIMARY KEY (id),
            CONSTRAINT unique_message_user_status UNIQUE (message_id, user_jid)
        );
    """)

    # Создаем индексы
    op.execute("CREATE INDEX IF NOT EXISTS idx_message_statuses_message_id ON message_statuses (message_id);")
    op.execute("CREATE INDEX IF NOT EXISTS idx_message_statuses_user_jid ON message_statuses (user_jid);")

    # Мигрируем данные из старой таблицы, если она существует
    op.execute("""
        DO $$
        BEGIN
            IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'message_status_old') THEN
                INSERT INTO message_statuses (message_id, user_jid, status, timestamp)
                SELECT
                    message_id,
                    user_jid,
                    CASE
                        WHEN is_read = true THEN 'read'
                        WHEN is_delivered = true THEN 'delivered'
                        ELSE 'sent'
                    END as status,
                    COALESCE(read_at, delivered_at, created_at) as timestamp
                FROM message_status_old
                ON CONFLICT (message_id, user_jid) DO NOTHING;

                -- Удаляем старую таблицу
                DROP TABLE message_status_old;
            END IF;
        END
        $$;
    """)


def downgrade() -> None:
    # Создаем старую структуру таблицы
    op.execute("""
        CREATE TABLE message_status (
            id UUID DEFAULT uuid_generate_v4() NOT NULL,
            message_id VARCHAR NOT NULL,
            user_jid VARCHAR NOT NULL,
            is_delivered BOOLEAN DEFAULT false,
            delivered_at TIMESTAMP WITH TIME ZONE,
            is_read BOOLEAN DEFAULT false,
            read_at TIMESTAMP WITH TIME ZONE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
            PRIMARY KEY (id),
            CONSTRAINT unique_message_user_status UNIQUE (message_id, user_jid)
        );
    """)

    # Мигрируем данные обратно
    op.execute("""
        INSERT INTO message_status (message_id, user_jid, is_delivered, is_read, delivered_at, read_at, created_at)
        SELECT
            message_id,
            user_jid,
            CASE WHEN status IN ('delivered', 'read') THEN true ELSE false END as is_delivered,
            CASE WHEN status = 'read' THEN true ELSE false END as is_read,
            CASE WHEN status IN ('delivered', 'read') THEN timestamp ELSE NULL END as delivered_at,
            CASE WHEN status = 'read' THEN timestamp ELSE NULL END as read_at,
            timestamp as created_at
        FROM message_statuses;
    """)

    # Удаляем новую таблицу
    op.drop_table('message_statuses')
