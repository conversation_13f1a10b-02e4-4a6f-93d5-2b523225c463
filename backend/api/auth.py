"""
API роутер для аутентификации
"""
from datetime import timed<PERSON><PERSON>
from fastapi import APIRouter, HTTPException, Depends
from sqlalchemy.ext.asyncio import AsyncSession

from database.connection import get_db
from models.user import UserExtended
from schemas.user import UserCreate, UserLogin
from services.ejabberd import EjabberdService
from services.redis import RedisService
from core.security import create_access_token
from core.config import settings

router = APIRouter()

# Инициализируем сервисы
ejabberd_service = EjabberdService()

def get_redis_service() -> RedisService:
    from app import get_redis_service as get_global_redis
    return get_global_redis()


@router.post("/register")
async def register_user(
    user_data: UserCreate,
    db: AsyncSession = Depends(get_db)
):
    """Регистрация нового пользователя"""
    try:
        # Создаем пользователя в eJabberd
        success = await ejabberd_service.register_user(user_data.username, user_data.password)
        if not success:
            raise HTTPException(status_code=400, detail="Failed to register user in eJabberd")
        
        # Сохраняем дополнительную информацию в нашей БД
        user = UserExtended(
            username=user_data.username,
            display_name=user_data.display_name,
            email=user_data.email,
            phone=user_data.phone
        )
        
        db.add(user)
        await db.commit()
        
        return {"success": True, "message": "User registered successfully"}
    
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/login")
async def login_user(user_data: UserLogin):
    """Авторизация пользователя"""
    try:
        # Проверяем пароль через eJabberd
        success = await ejabberd_service.check_password(user_data.username, user_data.password)
        
        if not success:
            raise HTTPException(status_code=401, detail="Invalid credentials")
        
        # Создаем JWT токен
        user_jid = f"{user_data.username}@{settings.ejabberd_domain}"
        access_token_expires = timedelta(minutes=settings.access_token_expire_minutes)
        access_token = create_access_token(
            data={"sub": user_jid}, 
            expires_delta=access_token_expires
        )
        
        # Обновляем статус в Redis
        redis_svc = RedisService()
        await redis_svc.set_user_status(user_jid, "online")
        
        return {
            "success": True,
            "access_token": access_token,
            "token_type": "bearer",
            "user_jid": user_jid
        }
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))
