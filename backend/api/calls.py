"""
API роутер для звонков
"""
import uuid
import json
from fastapi import APIRouter, HTTPException, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from database.connection import get_db
from models.call import Call
from schemas.call import Call<PERSON><PERSON>, CallInfo, CallResponse
from services.livekit import LiveKitService
from services.push_notifications import push_service
from core.security import get_current_user_jid
from core.config import settings

router = APIRouter()

# Инициализируем сервисы
livekit_service = LiveKitService()


@router.post("/create", response_model=CallResponse)
async def create_call(
    call_data: CallCreate,
    db: AsyncSession = Depends(get_db),
    current_user_jid: str = Depends(get_current_user_jid)
):
    """Создание звонка через LiveKit"""
    try:
        room_name = f"call-{uuid.uuid4()}"
        
        # Записываем в БД
        call = Call(
            livekit_room_id=room_name,
            call_type=call_data.call_type,
            initiator_jid=current_user_jid,
            participants=call_data.participants
        )
        
        db.add(call)
        await db.commit()
        await db.refresh(call)
        
        # Генерируем токены для участников
        all_participants = [current_user_jid] + call_data.participants
        tokens = livekit_service.generate_tokens_for_participants(room_name, all_participants)

        # Отправляем push уведомления участникам звонка
        for participant_jid in call_data.participants:
            await push_service.send_call_notification(
                caller_jid=current_user_jid,
                callee_jid=participant_jid,
                call_type=call_data.call_type,
                room_name=room_name
            )

        return CallResponse(
            call_id=str(call.id),
            room_name=room_name,
            livekit_url=settings.livekit_url,
            tokens=tokens
        )
    
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{call_id}", response_model=CallInfo)
async def get_call_info(
    call_id: str,
    db: AsyncSession = Depends(get_db),
    current_user_jid: str = Depends(get_current_user_jid)
):
    """Получение информации о звонке"""
    result = await db.execute(
        select(Call).where(Call.id == call_id)
    )
    call = result.scalar_one_or_none()
    
    if not call:
        raise HTTPException(status_code=404, detail="Call not found")
    
    return CallInfo.model_validate(call)


@router.post("/{call_id}/end")
async def end_call(
    call_id: str,
    db: AsyncSession = Depends(get_db),
    current_user_jid: str = Depends(get_current_user_jid)
):
    """Завершение звонка"""
    result = await db.execute(
        select(Call).where(Call.id == call_id)
    )
    call = result.scalar_one_or_none()
    
    if not call:
        raise HTTPException(status_code=404, detail="Call not found")
    
    # Обновляем статус звонка
    call.status = 'ended'
    # TODO: Вычислить duration_seconds на основе started_at и ended_at
    
    await db.commit()
    
    return {"success": True, "message": "Call ended"}
