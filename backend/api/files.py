"""
API роутер для файлов
"""
import os
import uuid
from fastapi import APIRouter, HTTPException, Depends, UploadFile, File
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from database.connection import get_db
from models.file import File as FileModel
from schemas.file import FileUploadResponse, FileUrlResponse
from services.s3 import S3Service
from core.security import get_current_user_jid

router = APIRouter()

# Инициализируем сервисы
s3_service = S3Service()


@router.post("/upload", response_model=FileUploadResponse)
async def upload_file(
    file: UploadFile = File(...),
    db: AsyncSession = Depends(get_db),
    current_user_jid: str = Depends(get_current_user_jid)
):
    """Загрузка файла в S3"""
    try:
        file_id = str(uuid.uuid4())
        file_ext = os.path.splitext(file.filename)[1]
        s3_key = f"files/{file_id}{file_ext}"
        
        # Загружаем в S3
        success = s3_service.upload_file(
            file_data=file.file,
            s3_key=s3_key,
            content_type=file.content_type,
            file_size=file.size
        )
        
        if not success:
            raise HTTPException(status_code=500, detail="Failed to upload file to S3")
        
        # Сохраняем метаданные в БД
        file_record = FileModel(
            filename=f"{file_id}{file_ext}",
            original_name=file.filename,
            content_type=file.content_type,
            size_bytes=file.size,
            s3_key=s3_key,
            uploaded_by=current_user_jid
        )
        
        db.add(file_record)
        await db.commit()
        await db.refresh(file_record)
        
        return FileUploadResponse(
            file_id=str(file_record.id),
            url=f"/api/files/{file_record.id}"
        )
    
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{file_id}", response_model=FileUrlResponse)
async def get_file(
    file_id: str,
    db: AsyncSession = Depends(get_db)
):
    """Получение файла"""
    result = await db.execute(
        select(FileModel).where(FileModel.id == file_id)
    )
    file_record = result.scalar_one_or_none()
    
    if not file_record:
        raise HTTPException(status_code=404, detail="File not found")
    
    # Генерируем presigned URL
    url = s3_service.get_presigned_url(file_record.s3_key)
    
    if not url:
        raise HTTPException(status_code=500, detail="Failed to generate file URL")
    
    return FileUrlResponse(url=url)
