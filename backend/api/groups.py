"""
API роутер для групповых чатов
"""
import uuid
from typing import List
from fastapi import APIRouter, HTTPException, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from sqlalchemy.orm import selectinload

from database.connection import get_db
from models.group import GroupChat, GroupMember
from schemas.group import GroupChatCreate, GroupInvite, GroupInfo, GroupListItem, GroupMemberInfo, GroupKick, GroupRoleUpdate
from services.ejabberd import EjabberdService
from services.push_notifications import push_service
from core.security import get_current_user_jid
from core.config import settings

router = APIRouter()

# Инициализируем сервисы
ejabberd_service = EjabberdService()


@router.post("/create")
async def create_group_chat(
    group_data: GroupChatCreate,
    db: AsyncSession = Depends(get_db),
    current_user_jid: str = Depends(get_current_user_jid)
):
    """Создание группового чата"""
    try:
        room_id = str(uuid.uuid4())
        room_jid = f"{room_id}@conference.{settings.ejabberd_domain}"
        
        # Создаем комнату в eJabberd
        success = await ejabberd_service.create_room(room_id, group_data.name)
        if not success:
            raise HTTPException(status_code=500, detail="Failed to create room in eJabberd")
        
        # Сохраняем в БД
        group = GroupChat(
            jid=room_jid,
            name=group_data.name,
            description=group_data.description,
            created_by=current_user_jid,
            is_public=group_data.is_public
        )
        
        db.add(group)
        await db.commit()
        await db.refresh(group)
        
        # Добавляем создателя как администратора
        admin_member = GroupMember(
            group_id=group.id,
            user_jid=current_user_jid,
            role='admin'
        )
        db.add(admin_member)
        
        # Добавляем других участников
        for member_jid in group_data.members:
            member = GroupMember(
                group_id=group.id,
                user_jid=member_jid
            )
            db.add(member)
        
        await db.commit()
        
        return {
            "success": True,
            "group_id": str(group.id),
            "jid": room_jid
        }
    
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/list", response_model=List[GroupListItem])
async def list_user_groups(
    db: AsyncSession = Depends(get_db),
    current_user_jid: str = Depends(get_current_user_jid)
):
    """Получение списка групп пользователя"""
    result = await db.execute(
        select(GroupChat, GroupMember.role, GroupMember.joined_at)
        .join(GroupMember)
        .where(GroupMember.user_jid == current_user_jid)
        .order_by(GroupChat.created_at.desc())
    )
    
    groups = []
    for group, role, joined_at in result:
        groups.append(GroupListItem(
            id=group.id,
            jid=group.jid,
            name=group.name,
            description=group.description,
            avatar_url=group.avatar_url,
            role=role,
            joined_at=joined_at,
            created_at=group.created_at
        ))
    
    return groups


@router.get("/{group_id}/info", response_model=GroupInfo)
async def get_group_info(
    group_id: str,
    db: AsyncSession = Depends(get_db),
    current_user_jid: str = Depends(get_current_user_jid)
):
    """Получение информации о группе"""
    # Проверяем, что пользователь является участником группы
    membership_result = await db.execute(
        select(GroupMember).where(
            GroupMember.group_id == group_id,
            GroupMember.user_jid == current_user_jid
        )
    )
    membership = membership_result.scalar_one_or_none()
    
    if not membership:
        raise HTTPException(status_code=403, detail="Not a group member")
    
    # Получаем информацию о группе
    result = await db.execute(
        select(GroupChat).where(GroupChat.id == group_id)
    )
    group = result.scalar_one_or_none()
    
    if not group:
        raise HTTPException(status_code=404, detail="Group not found")
    
    # Считаем участников
    member_count_result = await db.execute(
        select(GroupMember).where(GroupMember.group_id == group_id)
    )
    member_count = len(member_count_result.scalars().all())
    
    group_dict = {
        "id": group.id,
        "jid": group.jid,
        "name": group.name,
        "description": group.description,
        "avatar_url": group.avatar_url,
        "created_by": group.created_by,
        "max_members": group.max_members,
        "is_public": group.is_public,
        "created_at": group.created_at,
        "updated_at": group.updated_at,
        "member_count": member_count,
        "user_role": membership.role
    }

    return GroupInfo(**group_dict)


@router.post("/{group_id}/invite")
async def invite_to_group(
    group_id: str,
    invite_data: GroupInvite,
    db: AsyncSession = Depends(get_db),
    current_user_jid: str = Depends(get_current_user_jid)
):
    """Приглашение пользователей в группу"""
    try:
        # Проверяем, что пользователь является админом или модератором группы
        membership_result = await db.execute(
            select(GroupMember).where(
                GroupMember.group_id == group_id,
                GroupMember.user_jid == current_user_jid
            )
        )
        membership = membership_result.scalar_one_or_none()

        if not membership or membership.role not in ['admin', 'moderator']:
            raise HTTPException(status_code=403, detail="Insufficient permissions")

        # Добавляем новых участников
        added_members = []
        for user_jid in invite_data.user_jids:
            # Проверяем, что пользователь еще не в группе
            existing_member = await db.execute(
                select(GroupMember).where(
                    GroupMember.group_id == group_id,
                    GroupMember.user_jid == user_jid
                )
            )

            if existing_member.scalar_one_or_none():
                continue  # Пользователь уже в группе

            # Добавляем участника
            new_member = GroupMember(
                group_id=group_id,
                user_jid=user_jid,
                role='member'
            )
            db.add(new_member)
            added_members.append(user_jid)

        await db.commit()

        # Получаем информацию о группе для уведомлений
        group_result = await db.execute(
            select(GroupChat).where(GroupChat.id == group_id)
        )
        group = group_result.scalar_one_or_none()

        # Отправляем push уведомления приглашенным пользователям
        if group:
            for user_jid in added_members:
                await push_service.send_group_invite_notification(
                    inviter_jid=current_user_jid,
                    invitee_jid=user_jid,
                    group_name=group.name,
                    group_id=group_id
                )

        return {
            "success": True,
            "message": f"Added {len(added_members)} members",
            "added_members": added_members
        }

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{group_id}/kick")
async def kick_from_group(
    group_id: str,
    kick_data: GroupKick,
    db: AsyncSession = Depends(get_db),
    current_user_jid: str = Depends(get_current_user_jid)
):
    """Исключение пользователя из группы"""
    try:
        # Проверяем права доступа
        membership_result = await db.execute(
            select(GroupMember).where(
                GroupMember.group_id == group_id,
                GroupMember.user_jid == current_user_jid
            )
        )
        membership = membership_result.scalar_one_or_none()

        if not membership or membership.role not in ['admin', 'moderator']:
            raise HTTPException(status_code=403, detail="Insufficient permissions")

        # Находим участника для исключения
        target_member_result = await db.execute(
            select(GroupMember).where(
                GroupMember.group_id == group_id,
                GroupMember.user_jid == kick_data.user_jid
            )
        )
        target_member = target_member_result.scalar_one_or_none()

        if not target_member:
            raise HTTPException(status_code=404, detail="User not found in group")

        # Нельзя исключить админа (только другой админ может)
        if target_member.role == 'admin' and membership.role != 'admin':
            raise HTTPException(status_code=403, detail="Cannot kick admin")

        # Исключаем участника
        await db.delete(target_member)
        await db.commit()

        return {
            "success": True,
            "message": f"User {kick_data.user_jid} kicked from group"
        }

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{group_id}/role")
async def update_member_role(
    group_id: str,
    role_data: GroupRoleUpdate,
    db: AsyncSession = Depends(get_db),
    current_user_jid: str = Depends(get_current_user_jid)
):
    """Изменение роли участника группы"""
    try:
        # Проверяем, что пользователь является админом
        membership_result = await db.execute(
            select(GroupMember).where(
                GroupMember.group_id == group_id,
                GroupMember.user_jid == current_user_jid
            )
        )
        membership = membership_result.scalar_one_or_none()

        if not membership or membership.role != 'admin':
            raise HTTPException(status_code=403, detail="Only admins can change roles")

        # Находим участника для изменения роли
        target_member_result = await db.execute(
            select(GroupMember).where(
                GroupMember.group_id == group_id,
                GroupMember.user_jid == role_data.user_jid
            )
        )
        target_member = target_member_result.scalar_one_or_none()

        if not target_member:
            raise HTTPException(status_code=404, detail="User not found in group")

        # Проверяем валидность роли
        if role_data.role not in ['admin', 'moderator', 'member']:
            raise HTTPException(status_code=400, detail="Invalid role")

        # Обновляем роль
        target_member.role = role_data.role
        await db.commit()

        return {
            "success": True,
            "message": f"Role updated for {role_data.user_jid} to {role_data.role}"
        }

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=str(e))


