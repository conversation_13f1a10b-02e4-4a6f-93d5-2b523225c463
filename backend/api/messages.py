"""
API роутер для сообщений
"""
from fastapi import APIRouter, HTTPException, Depends
from sqlalchemy.ext.asyncio import AsyncSession

from database.connection import get_db
from schemas.message import MessageSend, MessageHistory
from schemas.message_advanced import (
    MessageReply, MessageForward, MessageEdit, MessageDelete,
    MessageStatusUpdate, TypingIndicator, MessageWithReactions
)
from models.message_thread import MessageThread
from models.message_status import MessageStatus
from models.message_reaction import MessageReaction
from services.ejabberd import EjabberdService
from services.push_notifications import push_service
from services.redis import RedisService
from core.security import get_current_user_jid

router = APIRouter()

# Инициализируем сервисы
ejabberd_service = EjabberdService()


@router.post("/send",
    summary="Отправка сообщения",
    description="""
    Отправляет сообщение через XMPP протокол.

    **Поддерживаемые типы сообщений:**
    - `chat` - личное сообщение
    - `groupchat` - сообщение в группу

    **Возможности:**
    - Текстовые сообщения
    - Прикрепление файлов (через file_id)
    - Автоматическая доставка через eJabberd
    - Push уведомления получателю

    **Формат JID получателя:**
    - Личные сообщения: `username@domain`
    - Групповые: `<EMAIL>`
    """,
    responses={
        200: {"description": "Сообщение успешно отправлено"},
        401: {"description": "Требуется авторизация"},
        422: {"description": "Ошибка валидации данных"},
        500: {"description": "Ошибка отправки через eJabberd"}
    }
)
async def send_message(
    message_data: MessageSend,
    current_user_jid: str = Depends(get_current_user_jid)
):
    """Отправка сообщения через eJabberd"""
    try:
        # Если есть файл, добавляем информацию о нем в сообщение
        body = message_data.body
        if message_data.file_id:
            body += f"\n[FILE:{message_data.file_id}]"

        success = await ejabberd_service.send_message(
            from_jid=current_user_jid,
            to_jid=message_data.to_jid,
            body=body,
            message_type=message_data.message_type
        )

        if not success:
            raise HTTPException(status_code=500, detail="Failed to send message")

        # Отправляем push уведомление
        await push_service.send_message_notification(
            from_jid=current_user_jid,
            to_jid=message_data.to_jid,
            message_body=message_data.body,
            message_type=message_data.message_type
        )

        return {"success": True, "message": "Message sent"}

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/history/{jid}", response_model=MessageHistory)
async def get_message_history(
    jid: str,
    limit: int = 50,
    offset: int = 0,
    current_user_jid: str = Depends(get_current_user_jid)
):
    """Получение истории сообщений через eJabberd MAM"""
    try:
        result = await ejabberd_service.get_message_history(
            user_jid=current_user_jid,
            with_jid=jid,
            limit=limit,
            offset=offset
        )
        
        return MessageHistory(messages=result.get("messages", []))
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/reply")
async def reply_to_message(
    reply_data: MessageReply,
    db: AsyncSession = Depends(get_db),
    current_user_jid: str = Depends(get_current_user_jid)
):
    """Ответ на сообщение"""
    try:
        # Отправляем сообщение через ejabberd
        ejabberd_service = EjabberdService()
        success = await ejabberd_service.send_message(
            from_jid=current_user_jid,
            to_jid=reply_data.to_jid,
            body=reply_data.body,
            message_type=reply_data.message_type
        )

        if not success:
            raise HTTPException(status_code=500, detail="Failed to send reply")

        # Сохраняем информацию об ответе в базе данных
        # Генерируем ID сообщения (в реальной системе это должно приходить от ejabberd)
        import uuid
        message_id = str(uuid.uuid4())

        thread_message = MessageThread(
            message_id=message_id,
            reply_to_message_id=reply_data.reply_to_message_id,
            user_jid=current_user_jid,
            body=reply_data.body,
            message_type=reply_data.message_type
        )

        db.add(thread_message)
        await db.commit()

        # Отправляем push уведомление
        await push_service.send_message_notification(
            from_jid=current_user_jid,
            to_jid=reply_data.to_jid,
            message_body=f"Ответ: {reply_data.body}",
            message_type=reply_data.message_type
        )

        return {"success": True, "message": "Reply sent", "message_id": message_id}

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/forward")
async def forward_message(
    forward_data: MessageForward,
    db: AsyncSession = Depends(get_db),
    current_user_jid: str = Depends(get_current_user_jid)
):
    """Пересылка сообщения"""
    try:
        # Получаем оригинальное сообщение из базы данных
        # В реальной системе нужно получать из ejabberd или кэша
        original_message_result = await db.execute(
            select(MessageThread).where(MessageThread.message_id == forward_data.message_id)
        )
        original_message = original_message_result.scalar_one_or_none()

        if not original_message:
            raise HTTPException(status_code=404, detail="Original message not found")

        # Формируем текст пересылаемого сообщения
        forwarded_body = f"Переслано от {original_message.user_jid}:\n{original_message.body}"

        # Отправляем сообщение через ejabberd
        ejabberd_service = EjabberdService()
        success = await ejabberd_service.send_message(
            from_jid=current_user_jid,
            to_jid=forward_data.to_jid,
            body=forwarded_body,
            message_type=forward_data.message_type
        )

        if not success:
            raise HTTPException(status_code=500, detail="Failed to forward message")

        # Сохраняем информацию о пересылке
        import uuid
        new_message_id = str(uuid.uuid4())

        forwarded_message = MessageThread(
            message_id=new_message_id,
            user_jid=current_user_jid,
            body=forwarded_body,
            message_type=forward_data.message_type,
            is_forwarded=True,
            forwarded_from_jid=original_message.user_jid,
            forwarded_from_message_id=forward_data.message_id
        )

        db.add(forwarded_message)
        await db.commit()

        # Отправляем push уведомление
        await push_service.send_message_notification(
            from_jid=current_user_jid,
            to_jid=forward_data.to_jid,
            message_body="Переслано сообщение",
            message_type=forward_data.message_type
        )

        return {"success": True, "message": "Message forwarded", "message_id": new_message_id}

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/edit")
async def edit_message(
    edit_data: MessageEdit,
    db: AsyncSession = Depends(get_db),
    current_user_jid: str = Depends(get_current_user_jid)
):
    """Редактирование сообщения"""
    try:
        # Находим сообщение в базе данных
        message_result = await db.execute(
            select(MessageThread).where(
                MessageThread.message_id == edit_data.message_id,
                MessageThread.user_jid == current_user_jid
            )
        )
        message = message_result.scalar_one_or_none()

        if not message:
            raise HTTPException(status_code=404, detail="Message not found or not owned by user")

        # Обновляем сообщение
        from datetime import datetime
        message.body = edit_data.new_body
        message.is_edited = True
        message.edited_at = datetime.utcnow()

        await db.commit()

        # В реальной системе здесь нужно обновить сообщение в ejabberd
        # и отправить уведомление о редактировании

        return {
            "success": True,
            "message": "Message edited",
            "edited_at": message.edited_at.isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/delete")
async def delete_message(
    delete_data: MessageDelete,
    db: AsyncSession = Depends(get_db),
    current_user_jid: str = Depends(get_current_user_jid)
):
    """Удаление сообщения"""
    try:
        # Находим сообщение в базе данных
        message_result = await db.execute(
            select(MessageThread).where(
                MessageThread.message_id == delete_data.message_id,
                MessageThread.user_jid == current_user_jid
            )
        )
        message = message_result.scalar_one_or_none()

        if not message:
            raise HTTPException(status_code=404, detail="Message not found or not owned by user")

        if delete_data.delete_for_everyone:
            # Удаляем сообщение полностью
            await db.delete(message)

            # Удаляем связанные реакции
            await db.execute(
                delete(MessageReaction).where(
                    MessageReaction.message_id == delete_data.message_id
                )
            )

            # Удаляем статусы сообщения
            await db.execute(
                delete(MessageStatus).where(
                    MessageStatus.message_id == delete_data.message_id
                )
            )
        else:
            # Помечаем как удаленное только для отправителя
            message.body = "Сообщение удалено"
            message.is_edited = True
            message.edited_at = datetime.utcnow()

        await db.commit()

        return {"success": True, "message": "Message deleted"}

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/status/update")
async def update_message_status(
    status_data: MessageStatusUpdate,
    db: AsyncSession = Depends(get_db),
    current_user_jid: str = Depends(get_current_user_jid)
):
    """Обновление статуса сообщения (доставлено/прочитано)"""
    try:
        # Ищем существующий статус
        status_result = await db.execute(
            select(MessageStatus).where(
                MessageStatus.message_id == status_data.message_id,
                MessageStatus.user_jid == current_user_jid
            )
        )
        status = status_result.scalar_one_or_none()

        from datetime import datetime
        now = datetime.utcnow()

        if status:
            # Обновляем существующий статус
            if status_data.is_delivered is not None:
                status.is_delivered = status_data.is_delivered
                if status_data.is_delivered:
                    status.delivered_at = now

            if status_data.is_read is not None:
                status.is_read = status_data.is_read
                if status_data.is_read:
                    status.read_at = now
                    # Автоматически помечаем как доставленное
                    status.is_delivered = True
                    if not status.delivered_at:
                        status.delivered_at = now
        else:
            # Создаем новый статус
            status = MessageStatus(
                message_id=status_data.message_id,
                user_jid=current_user_jid,
                is_delivered=status_data.is_delivered or False,
                delivered_at=now if status_data.is_delivered else None,
                is_read=status_data.is_read or False,
                read_at=now if status_data.is_read else None
            )
            db.add(status)

        await db.commit()

        return {"success": True, "message": "Status updated"}

    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/typing")
async def set_typing_indicator(
    typing_data: TypingIndicator,
    current_user_jid: str = Depends(get_current_user_jid)
):
    """Установка индикатора печати"""
    try:
        redis_service = RedisService()

        if typing_data.is_typing:
            # Устанавливаем индикатор печати на 10 секунд
            await redis_service.redis.setex(
                f"typing:{current_user_jid}:{typing_data.to_jid}",
                10,  # TTL в секундах
                "typing"
            )
        else:
            # Удаляем индикатор печати
            await redis_service.redis.delete(f"typing:{current_user_jid}:{typing_data.to_jid}")

        return {"success": True, "message": "Typing indicator updated"}

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/typing/{user_jid}")
async def get_typing_indicators(
    user_jid: str,
    current_user_jid: str = Depends(get_current_user_jid)
):
    """Получение индикаторов печати для пользователя"""
    try:
        redis_service = RedisService()

        # Ищем всех, кто печатает этому пользователю
        typing_users = []

        # Сканируем ключи typing:*:user_jid
        pattern = f"typing:*:{user_jid}"
        async for key in redis_service.redis.scan_iter(match=pattern):
            key_str = key.decode() if isinstance(key, bytes) else key
            # Извлекаем JID печатающего пользователя
            parts = key_str.split(':')
            if len(parts) == 3:
                typing_user_jid = parts[1]
                typing_users.append(typing_user_jid)

        return {"typing_users": typing_users}

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
