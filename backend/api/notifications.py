"""
API роутер для уведомлений
"""
from typing import List
from fastapi import APIRouter, HTTPException, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update

from database.connection import get_db
from models.notification import Notification
from schemas.notification import NotificationList, MarkNotificationsRead
from services.push_notifications import push_service
from core.security import get_current_user_jid

router = APIRouter()


@router.get("", response_model=NotificationList)
async def get_notifications(
    limit: int = 20,
    offset: int = 0,
    unread_only: bool = False,
    db: AsyncSession = Depends(get_db),
    current_user_jid: str = Depends(get_current_user_jid)
):
    """Получение уведомлений пользователя"""
    query = select(Notification).where(Notification.user_jid == current_user_jid)
    
    if unread_only:
        query = query.where(Notification.is_read == False)
    
    query = query.order_by(Notification.created_at.desc()).limit(limit).offset(offset)
    
    result = await db.execute(query)
    notifications = result.scalars().all()
    
    return NotificationList(notifications=notifications)


@router.post("/mark-read")
async def mark_notifications_read(
    mark_data: MarkNotificationsRead,
    db: AsyncSession = Depends(get_db),
    current_user_jid: str = Depends(get_current_user_jid)
):
    """Отметка уведомлений как прочитанных"""
    try:
        await db.execute(
            update(Notification)
            .where(
                Notification.id.in_(mark_data.notification_ids),
                Notification.user_jid == current_user_jid
            )
            .values(is_read=True)
        )
        
        await db.commit()
        
        return {
            "success": True, 
            "message": f"Marked {len(mark_data.notification_ids)} notifications as read"
        }
    
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/unread-count")
async def get_unread_count(current_user_jid: str = Depends(get_current_user_jid)):
    """Получение количества непрочитанных уведомлений"""
    try:
        count = await push_service.get_unread_count(current_user_jid)
        return {"unread_count": count}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
