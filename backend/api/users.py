"""
API роутер для пользователей
"""
from typing import List
from fastapi import APIRouter, HTTPException, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from database.connection import get_db
from models.user import UserExtended
from schemas.user import UserProfile, UserStatusUpdate, UserProfileUpdate, UserSearchResult
from services.redis import RedisService
from core.security import get_current_user_jid

router = APIRouter()

# Dependency для получения Redis сервиса
def get_redis_service() -> RedisService:
    from app import get_redis_service as get_global_redis
    return get_global_redis()


@router.get("/profile/{username}", response_model=UserProfile)
async def get_user_profile(
    username: str,
    db: AsyncSession = Depends(get_db),
    current_user_jid: str = Depends(get_current_user_jid)
):
    """Получение профиля пользователя"""
    result = await db.execute(
        select(UserExtended).where(UserExtended.username == username)
    )
    user = result.scalar_one_or_none()
    
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    
    return UserProfile.model_validate(user)


@router.post("/status")
async def update_user_status(
    status_data: UserStatusUpdate,
    db: AsyncSession = Depends(get_db),
    current_user_jid: str = Depends(get_current_user_jid)
):
    """Обновление статуса пользователя"""
    try:
        # Обновляем в Redis
        redis_svc = RedisService()
        await redis_svc.set_user_status(current_user_jid, status_data.status)
        
        # Обновляем в БД
        username = current_user_jid.split('@')[0]
        result = await db.execute(
            select(UserExtended).where(UserExtended.username == username)
        )
        user = result.scalar_one_or_none()
        
        if user:
            user.status = status_data.status
            await db.commit()
        
        return {"success": True, "status": status_data.status}
    
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/profile/update")
async def update_profile(
    profile_data: UserProfileUpdate,
    db: AsyncSession = Depends(get_db),
    current_user_jid: str = Depends(get_current_user_jid)
):
    """Обновление профиля пользователя"""
    try:
        username = current_user_jid.split('@')[0]
        result = await db.execute(
            select(UserExtended).where(UserExtended.username == username)
        )
        user = result.scalar_one_or_none()
        
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        # Обновляем только переданные поля
        if profile_data.display_name is not None:
            user.display_name = profile_data.display_name
        if profile_data.email is not None:
            user.email = profile_data.email
        if profile_data.phone is not None:
            user.phone = profile_data.phone
        
        await db.commit()
        return {"success": True, "message": "Profile updated"}
    
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/search", response_model=List[UserSearchResult])
async def search_users(
    q: str,
    limit: int = 10,
    db: AsyncSession = Depends(get_db),
    current_user_jid: str = Depends(get_current_user_jid)
):
    """Поиск пользователей"""
    result = await db.execute(
        select(UserExtended)
        .where(
            (UserExtended.username.ilike(f"%{q}%")) |
            (UserExtended.display_name.ilike(f"%{q}%"))
        )
        .limit(limit)
    )
    users = result.scalars().all()
    
    return [UserSearchResult.model_validate(user) for user in users]


@router.get("/online")
async def get_online_users(current_user_jid: str = Depends(get_current_user_jid)):
    """Получение списка онлайн пользователей"""
    try:
        redis_svc = RedisService()
        online_users = await redis_svc.get_online_users()
        
        result = []
        for user_jid, status in online_users.items():
            username = user_jid.split('@')[0]
            result.append({
                "user_jid": user_jid,
                "username": username,
                "status": status
            })
        
        return {"online_users": result}
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
