"""
Конфигурация приложения
"""
import os
from typing import Optional
from pydantic_settings import BaseSettings
from dotenv import load_dotenv

# Загружаем переменные окружения
load_dotenv()


class Settings(BaseSettings):
    """Настройки приложения"""
    
    # База данных
    database_url: str = os.getenv("DATABASE_URL", "postgresql://postgres:postgres123@localhost:5432/messenger")
    
    # Redis
    redis_url: str = os.getenv("REDIS_URL", "redis://localhost:6379")
    
    # MinIO S3
    minio_endpoint: str = os.getenv("MINIO_ENDPOINT", "localhost:9000")
    minio_access_key: str = os.getenv("MINIO_ACCESS_KEY", "minioadmin")
    minio_secret_key: str = os.getenv("MINIO_SECRET_KEY", "minioadmin123")
    minio_bucket: str = "messenger-files"
    
    # eJabberd
    ejabberd_api_url: str = os.getenv("EJABBERD_API_URL", "http://localhost:5280/api")
    ejabberd_admin_user: str = os.getenv("EJABBERD_ADMIN_USER", "admin")
    ejabberd_admin_password: str = os.getenv("EJABBERD_ADMIN_PASSWORD", "admin123")
    ejabberd_domain: str = "localhost"
    
    # LiveKit
    livekit_api_key: str = os.getenv("LIVEKIT_API_KEY", "APIKey")
    livekit_api_secret: str = os.getenv("LIVEKIT_API_SECRET", "secret123")
    livekit_url: str = os.getenv("LIVEKIT_URL", "ws://localhost:7880")
    
    # JWT
    secret_key: str = os.getenv("SECRET_KEY", "your-super-secret-key-change-in-production")
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    
    # Приложение
    app_name: str = "Messenger Backend"
    app_version: str = "1.0.0"
    debug: bool = os.getenv("DEBUG", "false").lower() == "true"
    
    class Config:
        env_file = ".env"


# Глобальный экземпляр настроек
settings = Settings()
