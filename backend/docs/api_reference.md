# 📚 API Reference

Полное описание REST API эндпойнтов мессенджера для разработчиков клиентов.

## 🔐 Аутентификация

Все защищенные эндпойнты требуют JWT токен в заголовке:
```
Authorization: Bearer <your_jwt_token>
```

### POST `/api/auth/register`
Регистрация нового пользователя

**Тело запроса:**
```json
{
  "username": "john_doe",
  "password": "secure_password",
  "display_name": "<PERSON>",
  "email": "<EMAIL>",
  "phone": "+1234567890"
}
```

**Ответ:**
```json
{
  "success": true,
  "message": "User registered successfully"
}
```

### POST `/api/auth/login`
Авторизация пользователя

**Тело запроса:**
```json
{
  "username": "john_doe",
  "password": "secure_password"
}
```

**Ответ:**
```json
{
  "success": true,
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer",
  "user_jid": "john_doe@localhost"
}
```

## 👤 Пользователи

### GET `/api/users/profile/{username}`
Получение профиля пользователя

**Параметры:**
- `username` - имя пользователя

**Ответ:**
```json
{
  "username": "john_doe",
  "display_name": "John Doe",
  "email": "<EMAIL>",
  "phone": "+1234567890",
  "avatar_url": "https://...",
  "status": "online",
  "last_seen": "2024-01-01T12:00:00Z",
  "created_at": "2024-01-01T10:00:00Z"
}
```

### GET `/api/users/search`
Поиск пользователей

**Параметры запроса:**
- `q` - поисковый запрос
- `limit` - лимит результатов (по умолчанию 10)

**Ответ:**
```json
[
  {
    "username": "john_doe",
    "display_name": "John Doe",
    "avatar_url": "https://..."
  }
]
```

### GET `/api/users/online`
Список онлайн пользователей

**Ответ:**
```json
{
  "online_users": [
    {
      "user_jid": "john_doe@localhost",
      "username": "john_doe",
      "status": "online"
    }
  ]
}
```

### POST `/api/users/status`
Обновление статуса пользователя

**Тело запроса:**
```json
{
  "status": "online",
  "status_message": "Working from home"
}
```

### POST `/api/users/profile/update`
Обновление профиля

**Тело запроса:**
```json
{
  "display_name": "John Smith",
  "email": "<EMAIL>",
  "phone": "+1234567891"
}
```

## 💬 Сообщения

### POST `/api/messages/send`
Отправка сообщения

**Тело запроса:**
```json
{
  "to_jid": "jane_doe@localhost",
  "body": "Hello, how are you?",
  "message_type": "chat",
  "file_id": "optional-file-uuid"
}
```

**Ответ:**
```json
{
  "success": true,
  "message": "Message sent successfully"
}
```

### GET `/api/messages/history/{jid}`
История сообщений с пользователем

**Параметры:**
- `jid` - JID собеседника
- `limit` - количество сообщений (по умолчанию 50)
- `offset` - смещение (по умолчанию 0)

**Ответ:**
```json
{
  "messages": [
    {
      "id": "msg-123",
      "from": "john_doe@localhost",
      "to": "jane_doe@localhost",
      "body": "Hello!",
      "timestamp": "2024-01-01T12:00:00Z",
      "message_type": "chat"
    }
  ]
}
```

### POST `/api/messages/reply`
Ответ на сообщение

**Тело запроса:**
```json
{
  "to_jid": "jane_doe@localhost",
  "body": "Thanks for the info!",
  "reply_to_message_id": "msg-123",
  "message_type": "chat"
}
```

### POST `/api/messages/forward`
Пересылка сообщения

**Тело запроса:**
```json
{
  "to_jid": "bob@localhost",
  "original_message_id": "msg-123",
  "comment": "Check this out"
}
```

### PUT `/api/messages/edit`
Редактирование сообщения

**Тело запроса:**
```json
{
  "message_id": "msg-123",
  "new_body": "Updated message text"
}
```

### DELETE `/api/messages/delete`
Удаление сообщения

**Тело запроса:**
```json
{
  "message_id": "msg-123",
  "delete_for_everyone": false
}
```

### POST `/api/messages/typing`
Индикатор печати

**Тело запроса:**
```json
{
  "to_jid": "jane_doe@localhost",
  "is_typing": true
}
```

## 👥 Группы

### POST `/api/groups/create`
Создание группового чата

**Тело запроса:**
```json
{
  "name": "Team Chat",
  "description": "Our team discussion",
  "is_public": true
}
```

**Ответ:**
```json
{
  "success": true,
  "group_id": "uuid-here",
  "group_jid": "<EMAIL>"
}
```

### GET `/api/groups/list`
Список групп пользователя

**Ответ:**
```json
{
  "groups": [
    {
      "id": "uuid-here",
      "name": "Team Chat",
      "description": "Our team discussion",
      "member_count": 5,
      "last_activity": "2024-01-01T12:00:00Z"
    }
  ]
}
```

### GET `/api/groups/{group_id}/info`
Информация о группе

**Ответ:**
```json
{
  "id": "uuid-here",
  "jid": "<EMAIL>",
  "name": "Team Chat",
  "description": "Our team discussion",
  "avatar_url": "https://...",
  "created_by": "john_doe@localhost",
  "max_members": 500,
  "is_public": true,
  "created_at": "2024-01-01T10:00:00Z",
  "member_count": 5,
  "user_role": "admin"
}
```

### GET `/api/groups/{group_id}/members`
Участники группы

**Ответ:**
```json
{
  "members": [
    {
      "user_jid": "john_doe@localhost",
      "role": "admin",
      "joined_at": "2024-01-01T10:00:00Z",
      "display_name": "John Doe",
      "avatar_url": "https://...",
      "status": "online"
    }
  ]
}
```

### POST `/api/groups/{group_id}/invite`
Приглашение в группу

**Тело запроса:**
```json
{
  "user_jid": "bob@localhost"
}
```

### DELETE `/api/groups/{group_id}/members/{user_jid}`
Удаление из группы

### PUT `/api/groups/{group_id}/members/{user_jid}/role`
Изменение роли участника

**Тело запроса:**
```json
{
  "role": "moderator"
}
```

## 📁 Файлы

### POST `/api/files/upload`
Загрузка файла

**Тело запроса:** multipart/form-data
- `file` - файл для загрузки

**Ответ:**
```json
{
  "file_id": "uuid-here",
  "url": "/api/files/uuid-here"
}
```

### GET `/api/files/{file_id}`
Получение файла

**Ответ:**
```json
{
  "url": "https://presigned-s3-url..."
}
```

## 📞 Звонки

### POST `/api/calls/create`
Создание звонка

**Тело запроса:**
```json
{
  "participants": ["jane_doe@localhost", "bob@localhost"],
  "call_type": "video"
}
```

**Ответ:**
```json
{
  "call_id": "uuid-here",
  "room_name": "call-room-id",
  "livekit_url": "ws://localhost:7880",
  "tokens": {
    "john_doe@localhost": "livekit-token-1",
    "jane_doe@localhost": "livekit-token-2"
  }
}
```

### GET `/api/calls/{call_id}`
Информация о звонке

**Ответ:**
```json
{
  "id": "uuid-here",
  "livekit_room_id": "call-room-id",
  "call_type": "video",
  "initiator_jid": "john_doe@localhost",
  "participants": ["jane_doe@localhost"],
  "status": "ongoing",
  "started_at": "2024-01-01T12:00:00Z",
  "duration_seconds": 120
}
```

### POST `/api/calls/{call_id}/end`
Завершение звонка

**Ответ:**
```json
{
  "success": true,
  "duration_seconds": 300
}
```

## 🔔 Уведомления

### GET `/api/notifications`
Получение уведомлений

**Параметры запроса:**
- `limit` - лимит (по умолчанию 20)
- `offset` - смещение (по умолчанию 0)
- `unread_only` - только непрочитанные (по умолчанию false)

**Ответ:**
```json
{
  "notifications": [
    {
      "id": "uuid-here",
      "type": "message",
      "title": "New message",
      "body": "John sent you a message",
      "data": {"from": "john_doe@localhost"},
      "is_read": false,
      "created_at": "2024-01-01T12:00:00Z"
    }
  ]
}
```

### POST `/api/notifications/mark-read`
Отметка как прочитанные

**Тело запроса:**
```json
{
  "notification_ids": ["uuid-1", "uuid-2"]
}
```

### GET `/api/notifications/unread-count`
Количество непрочитанных

**Ответ:**
```json
{
  "unread_count": 5
}
```

## 😊 Реакции на сообщения

### POST `/api/reactions/add`
Добавление реакции

**Тело запроса:**
```json
{
  "message_id": "msg-123",
  "emoji": "👍"
}
```

### DELETE `/api/reactions/remove/{message_id}`
Удаление реакции

### GET `/api/reactions/message/{message_id}`
Реакции на сообщение

**Ответ:**
```json
{
  "message_id": "msg-123",
  "reactions": {
    "👍": ["john_doe@localhost", "jane_doe@localhost"],
    "❤️": ["bob@localhost"]
  },
  "total_count": 3
}
```

## 📺 Каналы

### POST `/api/channels/create`
Создание канала

**Тело запроса:**
```json
{
  "name": "News Channel",
  "description": "Latest news and updates",
  "is_public": true
}
```

### GET `/api/channels/list`
Список каналов

### POST `/api/channels/{channel_id}/subscribe`
Подписка на канал

### DELETE `/api/channels/{channel_id}/unsubscribe`
Отписка от канала

### POST `/api/channels/{channel_id}/post`
Публикация в канале

**Тело запроса:**
```json
{
  "content": "Important announcement!",
  "media_urls": ["https://..."]
}
```

## 📊 Статусы сообщений

### POST `/api/message-status/update`
Обновление статуса сообщения

**Тело запроса:**
```json
{
  "message_id": "msg-123",
  "status": "read"
}
```

### GET `/api/message-status/conversation/{jid}`
Статусы сообщений в беседе

## 🔧 Системные эндпойнты

### GET `/api/health`
Проверка состояния API

**Ответ:**
```json
{
  "status": "healthy",
  "services": {
    "database": "ok",
    "redis": "ok",
    "s3": "ok"
  }
}
```

### GET `/api/stats`
Общая статистика

**Ответ:**
```json
{
  "global": {
    "total_users": 1000,
    "total_groups": 50,
    "total_files": 2000,
    "total_calls": 100
  }
}
```

## 🔗 WebHooks

### POST `/webhooks/livekit`
Webhook для событий LiveKit (только для внутреннего использования)

---

## 📝 Коды ошибок

- `400` - Неверный запрос
- `401` - Не авторизован
- `403` - Доступ запрещен
- `404` - Не найдено
- `422` - Ошибка валидации
- `500` - Внутренняя ошибка сервера

## 🚀 Примеры использования

### Полный цикл отправки сообщения:

1. Авторизация
2. Поиск пользователя
3. Отправка сообщения
4. Получение истории

```bash
# 1. Авторизация
curl -X POST http://localhost:8000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "john", "password": "pass"}'

# 2. Отправка сообщения
curl -X POST http://localhost:8000/api/messages/send \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"to_jid": "jane@localhost", "body": "Hello!"}'
```
