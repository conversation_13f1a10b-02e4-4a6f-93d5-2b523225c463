# 🗄️ База данных

Описание схемы PostgreSQL базы данных и системы миграций.

## 📊 Схема базы данных

### 👤 users_extended
Расширенная информация о пользователях (дополнение к eJabberd users)

```sql
CREATE TABLE users_extended (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username VARCHA<PERSON>(255) NOT NULL,
    server VARCHAR(255) NOT NULL DEFAULT 'localhost',
    display_name VARCHAR(255),
    avatar_url TEXT,
    phone VARCHAR(20),
    email VARCHAR(255),
    status VARCHAR(50) DEFAULT 'offline',
    last_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(username, server)
);
```

**Назначение:** Хранит дополнительную информацию о пользователях, которая не входит в стандартную схему eJabberd.

**Связи:** Связывается с eJabberd users через username@server

### 📁 files
Метаданные загруженных файлов

```sql
CREATE TABLE files (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    filename VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    content_type VARCHAR(100),
    size_bytes BIGINT,
    s3_key VARCHAR(500) NOT NULL,
    s3_bucket VARCHAR(100) DEFAULT 'messenger-files',
    uploaded_by VARCHAR(255) NOT NULL, -- JID пользователя
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Назначение:** Хранит метаданные файлов, загруженных в S3 хранилище.

**Связи:** `uploaded_by` ссылается на JID пользователя

### 👥 group_chats
Групповые чаты (дополнение к eJabberd MUC)

```sql
CREATE TABLE group_chats (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    jid VARCHAR(255) NOT NULL UNIQUE, -- <EMAIL>
    name VARCHAR(255) NOT NULL,
    description TEXT,
    avatar_url TEXT,
    created_by VARCHAR(255) NOT NULL, -- JID создателя
    max_members INTEGER DEFAULT 500,
    is_public BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Назначение:** Дополнительная информация о MUC комнатах eJabberd.

**Связи:** `created_by` ссылается на JID пользователя

### 👤 group_members
Участники групповых чатов

```sql
CREATE TABLE group_members (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    group_id UUID NOT NULL REFERENCES group_chats(id) ON DELETE CASCADE,
    user_jid VARCHAR(255) NOT NULL,
    role VARCHAR(20) DEFAULT 'member', -- admin, moderator, member
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Назначение:** Роли и метаданные участников групп.

**Связи:** 
- `group_id` → `group_chats.id`
- `user_jid` ссылается на JID пользователя

### 📞 calls
Аудио/видео звонки через LiveKit

```sql
CREATE TABLE calls (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    livekit_room_id VARCHAR(255) NOT NULL,
    call_type VARCHAR(20) NOT NULL, -- audio, video
    initiator_jid VARCHAR(255) NOT NULL,
    participants JSONB DEFAULT '[]', -- список JID участников
    status VARCHAR(20) DEFAULT 'initiated', -- initiated, ongoing, ended, declined
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    ended_at TIMESTAMP WITH TIME ZONE,
    duration_seconds INTEGER DEFAULT 0
);
```

**Назначение:** Метаданные звонков и связь с LiveKit комнатами.

**Связи:** `initiator_jid` ссылается на JID пользователя

### 🔔 notifications
Push уведомления

```sql
CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_jid VARCHAR(255) NOT NULL,
    type VARCHAR(50) NOT NULL, -- message, call, group_invite, etc.
    title VARCHAR(255) NOT NULL,
    body TEXT NOT NULL,
    data JSONB DEFAULT '{}', -- дополнительные данные
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Назначение:** Хранение push уведомлений для пользователей.

**Связи:** `user_jid` ссылается на JID пользователя

### 😊 message_reactions
Реакции на сообщения

```sql
CREATE TABLE message_reactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    message_id VARCHAR(255) NOT NULL, -- ID сообщения из eJabberd
    user_jid VARCHAR(255) NOT NULL,
    emoji VARCHAR(10) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(message_id, user_jid) -- один пользователь = одна реакция на сообщение
);
```

**Назначение:** Эмодзи реакции пользователей на сообщения.

**Связи:** 
- `message_id` ссылается на сообщение в eJabberd
- `user_jid` ссылается на JID пользователя

### 📊 message_statuses
Статусы доставки и прочтения сообщений

```sql
CREATE TABLE message_statuses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    message_id VARCHAR(255) NOT NULL,
    user_jid VARCHAR(255) NOT NULL, -- кому предназначено сообщение
    status VARCHAR(20) NOT NULL, -- sent, delivered, read
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(message_id, user_jid)
);
```

**Назначение:** Отслеживание статусов доставки и прочтения сообщений.

**Связи:**
- `message_id` ссылается на сообщение в eJabberd
- `user_jid` ссылается на JID получателя

### 🧵 message_threads
Треды сообщений (ответы)

```sql
CREATE TABLE message_threads (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    parent_message_id VARCHAR(255) NOT NULL,
    reply_message_id VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(reply_message_id) -- каждое сообщение может быть ответом только на одно
);
```

**Назначение:** Связи между сообщениями для создания тредов.

**Связи:** Оба поля ссылаются на сообщения в eJabberd

### 📺 channels
Broadcast каналы

```sql
CREATE TABLE channels (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    avatar_url TEXT,
    created_by VARCHAR(255) NOT NULL,
    is_public BOOLEAN DEFAULT TRUE,
    subscriber_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Назначение:** Каналы для массовой рассылки сообщений.

**Связи:** `created_by` ссылается на JID создателя

### 👥 channel_subscribers
Подписчики каналов

```sql
CREATE TABLE channel_subscribers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    channel_id UUID NOT NULL REFERENCES channels(id) ON DELETE CASCADE,
    user_jid VARCHAR(255) NOT NULL,
    subscribed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(channel_id, user_jid)
);
```

**Назначение:** Подписки пользователей на каналы.

**Связи:**
- `channel_id` → `channels.id`
- `user_jid` ссылается на JID пользователя

## 📈 Индексы для производительности

```sql
-- Пользователи
CREATE INDEX idx_users_extended_username_server ON users_extended(username, server);

-- Файлы
CREATE INDEX idx_files_uploaded_by ON files(uploaded_by);

-- Группы
CREATE INDEX idx_group_members_user_jid ON group_members(user_jid);

-- Звонки
CREATE INDEX idx_calls_initiator_jid ON calls(initiator_jid);

-- Уведомления
CREATE INDEX idx_notifications_user_jid ON notifications(user_jid);
CREATE INDEX idx_notifications_is_read ON notifications(is_read);

-- Реакции
CREATE INDEX idx_message_reactions_message_id ON message_reactions(message_id);

-- Статусы сообщений
CREATE INDEX idx_message_statuses_message_id ON message_statuses(message_id);

-- Каналы
CREATE INDEX idx_channel_subscribers_user_jid ON channel_subscribers(user_jid);
```

## 🔄 Система миграций (Alembic)

### Настройка Alembic

Конфигурация в `alembic.ini`:
```ini
[alembic]
script_location = alembic
sqlalchemy.url = postgresql://postgres:postgres123@localhost:5432/messenger
```

### Команды миграций

#### Создание новой миграции:
```bash
cd backend
alembic revision --autogenerate -m "Описание изменений"
```

#### Применение миграций:
```bash
# Применить все миграции
alembic upgrade head

# Применить до конкретной ревизии
alembic upgrade <revision_id>

# Откатить на одну миграцию назад
alembic downgrade -1
```

#### Просмотр истории:
```bash
# Текущая ревизия
alembic current

# История миграций
alembic history

# Показать SQL без выполнения
alembic upgrade head --sql
```

### Структура миграций

```
alembic/
├── versions/
│   └── 30111875268c_initial_migration.py  # Начальная миграция
├── env.py          # Конфигурация окружения
└── script.py.mako  # Шаблон для новых миграций
```

### Пример миграции:

```python
"""Add user avatar field

Revision ID: abc123
Revises: 30111875268c
Create Date: 2024-01-01 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers
revision = 'abc123'
down_revision = '30111875268c'

def upgrade() -> None:
    op.add_column('users_extended', 
        sa.Column('avatar_url', sa.Text(), nullable=True))

def downgrade() -> None:
    op.drop_column('users_extended', 'avatar_url')
```

## 🔗 Связи с внешними системами

### eJabberd база данных
- **users** - основная таблица пользователей
- **muc_room** - MUC комнаты
- **archive** - архив сообщений (MAM)
- **roster** - контакт-листы

### Интеграция:
- Наша БД дополняет eJabberd данными
- JID используется как внешний ключ
- Синхронизация через API и прямые запросы

## 🚀 Оптимизация производительности

### Стратегии:
1. **Индексы** на часто используемые поля
2. **Партиционирование** больших таблиц
3. **Кэширование** в Redis
4. **Connection pooling** для PostgreSQL
5. **Асинхронные запросы** через SQLAlchemy async

### Мониторинг:
- Медленные запросы в PostgreSQL
- Размер таблиц и индексов
- Статистика использования индексов
- Блокировки и deadlock'и

## 🔒 Безопасность данных

### Принципы:
1. **Валидация** всех входящих данных
2. **Параметризованные запросы** (защита от SQL injection)
3. **Ограничение прав** пользователя БД
4. **Шифрование** чувствительных данных
5. **Аудит** изменений критичных таблиц

### Backup стратегия:
- Ежедневные полные бэкапы
- Инкрементальные бэкапы каждые 4 часа
- Point-in-time recovery
- Тестирование восстановления

## 📊 Аналитика и метрики

### Полезные запросы:

#### Активность пользователей:
```sql
SELECT 
    DATE(created_at) as date,
    COUNT(*) as new_users
FROM users_extended 
GROUP BY DATE(created_at)
ORDER BY date DESC;
```

#### Популярные группы:
```sql
SELECT 
    gc.name,
    COUNT(gm.id) as member_count
FROM group_chats gc
LEFT JOIN group_members gm ON gc.id = gm.group_id
GROUP BY gc.id, gc.name
ORDER BY member_count DESC;
```

#### Статистика файлов:
```sql
SELECT 
    content_type,
    COUNT(*) as file_count,
    SUM(size_bytes) as total_size
FROM files
GROUP BY content_type
ORDER BY file_count DESC;
```

## 🔧 Обслуживание

### Регулярные задачи:
1. **VACUUM** и **ANALYZE** таблиц
2. **Reindex** для оптимизации индексов
3. **Очистка** старых уведомлений
4. **Архивирование** старых звонков
5. **Мониторинг** размера БД

### Скрипты обслуживания:
```sql
-- Очистка старых уведомлений (старше 30 дней)
DELETE FROM notifications 
WHERE created_at < NOW() - INTERVAL '30 days' 
AND is_read = true;

-- Обновление статистики
ANALYZE;

-- Переиндексация
REINDEX DATABASE messenger;
```
