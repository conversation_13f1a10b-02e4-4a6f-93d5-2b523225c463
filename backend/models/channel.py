"""
Модель каналов (broadcast channels)
"""
from sqlalchemy import Column, String, DateTime, Text, Boolean, Integer
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from datetime import datetime
import uuid

from database.connection import Base


class Channel(Base):
    """Каналы для broadcast сообщений"""
    __tablename__ = "channels"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(100), nullable=False)                  # Название канала
    username = Column(String(50), unique=True, nullable=True)   # @username канала
    description = Column(Text, nullable=True)                   # Описание канала
    owner_jid = Column(String, nullable=False, index=True)      # Владелец канала
    is_public = Column(Boolean, default=True)                   # Публичный канал
    is_verified = Column(Boolean, default=False)                # Верифицированный канал
    subscribers_count = Column(Integer, default=0)              # Количество подписчиков
    avatar_url = Column(String, nullable=True)                  # Аватар канала
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def to_dict(self):
        return {
            "id": str(self.id),
            "name": self.name,
            "username": self.username,
            "description": self.description,
            "owner_jid": self.owner_jid,
            "is_public": self.is_public,
            "is_verified": self.is_verified,
            "subscribers_count": self.subscribers_count,
            "avatar_url": self.avatar_url,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }


class ChannelSubscriber(Base):
    """Подписчики каналов"""
    __tablename__ = "channel_subscribers"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    channel_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    user_jid = Column(String, nullable=False, index=True)
    is_admin = Column(Boolean, default=False)                   # Администратор канала
    can_post = Column(Boolean, default=False)                   # Может публиковать
    subscribed_at = Column(DateTime, default=datetime.utcnow)

    def to_dict(self):
        return {
            "id": str(self.id),
            "channel_id": str(self.channel_id),
            "user_jid": self.user_jid,
            "is_admin": self.is_admin,
            "can_post": self.can_post,
            "subscribed_at": self.subscribed_at.isoformat() if self.subscribed_at else None
        }
