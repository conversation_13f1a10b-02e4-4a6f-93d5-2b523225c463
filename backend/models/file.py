"""
Модель файла
"""
from sqlalchemy import Column, String, BigInteger, DateTime, Text
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
import uuid

from database.connection import Base


class File(Base):
    """Файлы, загруженные пользователями"""
    
    __tablename__ = "files"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    filename = Column(String(255), nullable=False)
    original_name = Column(String(255), nullable=False)
    content_type = Column(String(100))
    size_bytes = Column(BigInteger)
    s3_key = Column(String(500), nullable=False)
    s3_bucket = Column(String(100), default='messenger-files')
    uploaded_by = Column(String(255), nullable=False)  # JID пользователя
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    def __repr__(self):
        return f"<File(filename='{self.filename}', uploaded_by='{self.uploaded_by}')>"
