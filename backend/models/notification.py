"""
Модель уведомлений
"""
from sqlalchemy import Column, String, Text, Boolean, DateTime, JSON, Index
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
import uuid

from database.connection import Base


class Notification(Base):
    """Уведомления пользователей"""
    
    __tablename__ = "notifications"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_jid = Column(String(255), nullable=False)
    type = Column(String(50), nullable=False)  # message, call, group_invite
    title = Column(String(255))
    body = Column(Text)
    data = Column(JSON, default=dict)
    is_read = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    __table_args__ = (
        Index('idx_notifications_user_read', 'user_jid', 'is_read'),
    )
    
    def __repr__(self):
        return f"<Notification(user_jid='{self.user_jid}', type='{self.type}', is_read={self.is_read})>"
