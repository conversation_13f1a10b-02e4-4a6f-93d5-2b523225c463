"""
Модель пользователя
"""
from sqlalchemy import Column, String, DateTime, Text, UniqueConstraint
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
import uuid

from database.connection import Base


class UserExtended(Base):
    """Расширенная информация о пользователе (дополнение к eJabberd users)"""
    
    __tablename__ = "users_extended"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    username = Column(String(255), nullable=False)
    server = Column(String(255), nullable=False, default='localhost')
    display_name = Column(String(255))
    avatar_url = Column(Text)
    phone = Column(String(20))
    email = Column(String(255))
    status = Column(String(50), default='offline')
    last_seen = Column(DateTime(timezone=True), server_default=func.now())
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    __table_args__ = (
        UniqueConstraint('username', 'server', name='uq_username_server'),
    )
    
    @property
    def jid(self) -> str:
        """Полный JID пользователя"""
        return f"{self.username}@{self.server}"
    
    def __repr__(self):
        return f"<UserExtended(username='{self.username}', display_name='{self.display_name}')>"
