"""
Схемы звонков
"""
from datetime import datetime
from typing import List, Optional, Dict
from pydantic import BaseModel
from uuid import UUID


class CallCreate(BaseModel):
    """Схема для создания звонка"""
    participants: List[str]  # список JID
    call_type: str = "video"  # audio или video


class CallInfo(BaseModel):
    """Информация о звонке"""
    id: UUID
    livekit_room_id: str
    call_type: str
    initiator_jid: str
    participants: List[str]
    status: str
    started_at: datetime
    ended_at: Optional[datetime] = None
    duration_seconds: int
    
    class Config:
        from_attributes = True


class CallResponse(BaseModel):
    """Ответ при создании звонка"""
    call_id: str
    room_name: str
    livekit_url: str
    tokens: Dict[str, str]
