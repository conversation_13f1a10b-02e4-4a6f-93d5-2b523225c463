"""
Схемы для каналов
"""
from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime


class ChannelCreate(BaseModel):
    """Создание канала"""
    name: str = Field(..., min_length=1, max_length=100, description="Название канала")
    username: Optional[str] = Field(None, max_length=50, description="@username канала")
    description: Optional[str] = Field(None, description="Описание канала")
    is_public: bool = Field(default=True, description="Публичный канал")


class ChannelUpdate(BaseModel):
    """Обновление канала"""
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    username: Optional[str] = Field(None, max_length=50)
    description: Optional[str] = None
    is_public: Optional[bool] = None
    avatar_url: Optional[str] = None


class ChannelInfo(BaseModel):
    """Информация о канале"""
    id: str
    name: str
    username: Optional[str] = None
    description: Optional[str] = None
    owner_jid: str
    is_public: bool
    is_verified: bool
    subscribers_count: int
    avatar_url: Optional[str] = None
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
    is_subscribed: bool = False  # Подписан ли текущий пользователь
    can_post: bool = False       # Может ли текущий пользователь публиковать


class ChannelListItem(BaseModel):
    """Элемент списка каналов"""
    id: str
    name: str
    username: Optional[str] = None
    description: Optional[str] = None
    subscribers_count: int
    is_verified: bool
    avatar_url: Optional[str] = None
    is_subscribed: bool = False


class ChannelSubscribe(BaseModel):
    """Подписка на канал"""
    channel_id: str = Field(..., description="ID канала")


class ChannelUnsubscribe(BaseModel):
    """Отписка от канала"""
    channel_id: str = Field(..., description="ID канала")


class ChannelPost(BaseModel):
    """Публикация в канале"""
    channel_id: str = Field(..., description="ID канала")
    body: str = Field(..., min_length=1, description="Текст сообщения")
    message_type: str = Field(default="channel_post", description="Тип сообщения")


class ChannelMemberUpdate(BaseModel):
    """Обновление прав участника канала"""
    user_jid: str = Field(..., description="JID пользователя")
    is_admin: Optional[bool] = None
    can_post: Optional[bool] = None


class ChannelSubscriber(BaseModel):
    """Подписчик канала"""
    id: str
    user_jid: str
    is_admin: bool
    can_post: bool
    subscribed_at: Optional[str] = None


class ChannelStats(BaseModel):
    """Статистика канала"""
    channel_id: str
    subscribers_count: int
    posts_count: int
    views_count: int
    growth_rate: float = 0.0  # Процент роста подписчиков


class ChannelSearch(BaseModel):
    """Поиск каналов"""
    query: str = Field(..., min_length=1, description="Поисковый запрос")
    is_public_only: bool = Field(default=True, description="Только публичные каналы")
    limit: int = Field(default=20, le=100, description="Лимит результатов")


class ChannelMessage(BaseModel):
    """Сообщение в канале"""
    id: str
    channel_id: str
    author_jid: str
    body: str
    message_type: str
    views_count: int = 0
    reactions: dict = Field(default_factory=dict)
    created_at: Optional[str] = None
    edited_at: Optional[str] = None
    is_pinned: bool = False
