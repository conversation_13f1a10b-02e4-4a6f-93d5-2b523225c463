"""
Схемы файлов
"""
from datetime import datetime
from pydantic import BaseModel
from uuid import UUID


class FileInfo(BaseModel):
    """Информация о файле"""
    id: UUID
    filename: str
    original_name: str
    content_type: str
    size_bytes: int
    uploaded_by: str
    created_at: datetime
    
    class Config:
        from_attributes = True


class FileUploadResponse(BaseModel):
    """Ответ при загрузке файла"""
    file_id: str
    url: str


class FileUrlResponse(BaseModel):
    """Ответ с URL файла"""
    url: str
