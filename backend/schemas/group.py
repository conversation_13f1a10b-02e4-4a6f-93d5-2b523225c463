"""
Схемы групповых чатов
"""
from datetime import datetime
from typing import List, Optional
from pydantic import BaseModel
from uuid import UUID


class GroupChatCreate(BaseModel):
    """Схема для создания группового чата"""
    name: str
    description: Optional[str] = None
    is_public: bool = True
    members: List[str] = []  # список JID


class GroupInvite(BaseModel):
    """Схема для приглашения в группу"""
    user_jids: List[str]


class GroupKick(BaseModel):
    """Схема для исключения из группы"""
    user_jid: str


class GroupRoleUpdate(BaseModel):
    """Схема для изменения роли участника"""
    user_jid: str
    role: str  # admin, moderator, member


class GroupMemberInfo(BaseModel):
    """Информация об участнике группы"""
    user_jid: str
    role: str
    joined_at: datetime
    display_name: Optional[str] = None
    avatar_url: Optional[str] = None
    status: Optional[str] = None
    
    class Config:
        from_attributes = True


class GroupInfo(BaseModel):
    """Информация о группе"""
    id: UUID
    jid: str
    name: str
    description: Optional[str] = None
    avatar_url: Optional[str] = None
    created_by: str
    max_members: int
    is_public: bool
    created_at: datetime
    updated_at: datetime
    member_count: Optional[int] = None
    user_role: Optional[str] = None
    
    class Config:
        from_attributes = True


class GroupListItem(BaseModel):
    """Элемент списка групп пользователя"""
    id: UUID
    jid: str
    name: str
    description: Optional[str] = None
    avatar_url: Optional[str] = None
    role: str
    joined_at: datetime
    created_at: datetime
    
    class Config:
        from_attributes = True
