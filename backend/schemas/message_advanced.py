"""
Схемы для расширенных функций сообщений
"""
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime


class MessageReactionCreate(BaseModel):
    """Создание реакции на сообщение"""
    message_id: str = Field(..., description="ID сообщения")
    emoji: str = Field(..., max_length=10, description="Эмодзи реакции")


class MessageReactionResponse(BaseModel):
    """Ответ с реакцией"""
    id: str
    message_id: str
    user_jid: str
    emoji: str
    created_at: Optional[str] = None


class MessageReactionSummary(BaseModel):
    """Сводка реакций на сообщение"""
    message_id: str
    reactions: Dict[str, List[str]] = Field(default_factory=dict, description="emoji -> [user_jids]")
    total_count: int = 0


class MessageReply(BaseModel):
    """Ответ на сообщение"""
    to_jid: str = Field(..., description="Получатель")
    body: str = Field(..., min_length=1, description="Текст сообщения")
    reply_to_message_id: str = Field(..., description="ID сообщения, на которое отвечаем")
    message_type: str = Field(default="chat", description="Тип сообщения")


class MessageForward(BaseModel):
    """Пересылка сообщения"""
    to_jid: str = Field(..., description="Получатель")
    message_id: str = Field(..., description="ID пересылаемого сообщения")
    message_type: str = Field(default="chat", description="Тип сообщения")


class MessageEdit(BaseModel):
    """Редактирование сообщения"""
    message_id: str = Field(..., description="ID сообщения")
    new_body: str = Field(..., min_length=1, description="Новый текст сообщения")


class MessageDelete(BaseModel):
    """Удаление сообщения"""
    message_id: str = Field(..., description="ID сообщения")
    delete_for_everyone: bool = Field(default=False, description="Удалить для всех")


class MessageStatusUpdate(BaseModel):
    """Обновление статуса сообщения"""
    message_id: str = Field(..., description="ID сообщения")
    is_delivered: Optional[bool] = None
    is_read: Optional[bool] = None


class TypingIndicator(BaseModel):
    """Индикатор печати"""
    to_jid: str = Field(..., description="Кому показать индикатор")
    is_typing: bool = Field(..., description="Печатает или нет")


class MessageWithReactions(BaseModel):
    """Сообщение с реакциями"""
    id: str
    from_jid: str
    to_jid: str
    body: str
    message_type: str
    timestamp: Optional[str] = None
    reply_to_message_id: Optional[str] = None
    is_forwarded: bool = False
    forwarded_from_jid: Optional[str] = None
    is_edited: bool = False
    edited_at: Optional[str] = None
    reactions: Dict[str, List[str]] = Field(default_factory=dict)
    read_by: List[str] = Field(default_factory=list)
    delivered_to: List[str] = Field(default_factory=list)


class ThreadMessage(BaseModel):
    """Сообщение в треде"""
    id: str
    message_id: str
    reply_to_message_id: Optional[str] = None
    thread_id: Optional[str] = None
    user_jid: str
    body: str
    message_type: str = "chat"
    is_forwarded: bool = False
    forwarded_from_jid: Optional[str] = None
    is_edited: bool = False
    edited_at: Optional[str] = None
    created_at: Optional[str] = None


class MessageThread(BaseModel):
    """Тред сообщений"""
    thread_id: str
    original_message: ThreadMessage
    replies: List[ThreadMessage] = Field(default_factory=list)
    replies_count: int = 0
    participants: List[str] = Field(default_factory=list)
