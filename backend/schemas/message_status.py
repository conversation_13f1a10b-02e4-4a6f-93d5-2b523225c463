"""
Схемы для статусов сообщений
"""
from pydantic import BaseModel, Field
from datetime import datetime
from typing import List, Literal
from uuid import UUID


class MessageStatusUpdate(BaseModel):
    """Схема для обновления статуса сообщения"""
    message_id: str = Field(..., description="ID сообщения")
    status: Literal["sent", "delivered", "read"] = Field(..., description="Статус сообщения")
    
    class Config:
        json_schema_extra = {
            "example": {
                "message_id": "msg_123456789",
                "status": "read"
            }
        }


class MessageStatusResponse(BaseModel):
    """Схема ответа со статусом сообщения"""
    id: UUID = Field(..., description="ID записи статуса")
    message_id: str = Field(..., description="ID сообщения")
    user_jid: str = Field(..., description="JID пользователя")
    status: str = Field(..., description="Статус сообщения")
    timestamp: datetime = Field(..., description="Время обновления статуса")
    
    class Config:
        from_attributes = True
        json_schema_extra = {
            "example": {
                "id": "550e8400-e29b-41d4-a716-************",
                "message_id": "msg_123456789",
                "user_jid": "alice@localhost",
                "status": "read",
                "timestamp": "2025-09-04T16:30:00Z"
            }
        }


class MessageStatusBatch(BaseModel):
    """Схема для массового обновления статусов"""
    updates: List[MessageStatusUpdate] = Field(..., description="Список обновлений статусов")
    
    class Config:
        json_schema_extra = {
            "example": {
                "updates": [
                    {"message_id": "msg_123", "status": "delivered"},
                    {"message_id": "msg_124", "status": "read"},
                    {"message_id": "msg_125", "status": "delivered"}
                ]
            }
        }


class UserStatus(BaseModel):
    """Статус пользователя для сообщения"""
    user_jid: str = Field(..., description="JID пользователя")
    timestamp: datetime = Field(..., description="Время статуса")
    
    class Config:
        json_schema_extra = {
            "example": {
                "user_jid": "alice@localhost",
                "timestamp": "2025-09-04T16:30:00Z"
            }
        }


class MessageStatusDetails(BaseModel):
    """Детальная информация о статусах сообщения"""
    message_id: str = Field(..., description="ID сообщения")
    delivered_to: List[UserStatus] = Field(..., description="Доставлено пользователям")
    read_by: List[UserStatus] = Field(..., description="Прочитано пользователями")
    delivery_count: int = Field(..., description="Количество доставок")
    read_count: int = Field(..., description="Количество прочтений")
    
    class Config:
        json_schema_extra = {
            "example": {
                "message_id": "msg_123456789",
                "delivered_to": [
                    {"user_jid": "alice@localhost", "timestamp": "2025-09-04T16:30:00Z"},
                    {"user_jid": "bob@localhost", "timestamp": "2025-09-04T16:30:05Z"}
                ],
                "read_by": [
                    {"user_jid": "alice@localhost", "timestamp": "2025-09-04T16:35:00Z"}
                ],
                "delivery_count": 2,
                "read_count": 1
            }
        }


class ConversationMessageStatus(BaseModel):
    """Статус сообщения в разговоре"""
    message_id: str = Field(..., description="ID сообщения")
    from_jid: str = Field(..., description="Отправитель")
    timestamp: str = Field(..., description="Время сообщения")
    status: str = Field(..., description="Статус сообщения")
    is_read: bool = Field(..., description="Прочитано ли сообщение")
    
    class Config:
        json_schema_extra = {
            "example": {
                "message_id": "msg_123456789",
                "from_jid": "alice@localhost",
                "timestamp": "2025-09-04T16:30:00Z",
                "status": "read",
                "is_read": True
            }
        }


class ConversationStatusResponse(BaseModel):
    """Статусы сообщений в разговоре"""
    peer_jid: str = Field(..., description="JID собеседника")
    messages: List[ConversationMessageStatus] = Field(..., description="Сообщения со статусами")
    unread_count: int = Field(..., description="Количество непрочитанных сообщений")
    
    class Config:
        json_schema_extra = {
            "example": {
                "peer_jid": "alice@localhost",
                "messages": [
                    {
                        "message_id": "msg_123",
                        "from_jid": "alice@localhost",
                        "timestamp": "2025-09-04T16:30:00Z",
                        "status": "read",
                        "is_read": True
                    },
                    {
                        "message_id": "msg_124",
                        "from_jid": "bob@localhost",
                        "timestamp": "2025-09-04T16:31:00Z",
                        "status": "delivered",
                        "is_read": False
                    }
                ],
                "unread_count": 1
            }
        }


class UnreadCountResponse(BaseModel):
    """Количество непрочитанных сообщений"""
    unread_count: int = Field(..., description="Общее количество непрочитанных")
    user_jid: str = Field(..., description="JID пользователя")
    
    class Config:
        json_schema_extra = {
            "example": {
                "unread_count": 5,
                "user_jid": "alice@localhost"
            }
        }


class ConversationUnreadCount(BaseModel):
    """Непрочитанные сообщения по разговорам"""
    peer_jid: str = Field(..., description="JID собеседника")
    unread_count: int = Field(..., description="Количество непрочитанных")
    last_message_timestamp: datetime = Field(..., description="Время последнего сообщения")
    
    class Config:
        json_schema_extra = {
            "example": {
                "peer_jid": "alice@localhost",
                "unread_count": 3,
                "last_message_timestamp": "2025-09-04T16:30:00Z"
            }
        }


class UnreadSummaryResponse(BaseModel):
    """Сводка по непрочитанным сообщениям"""
    total_unread: int = Field(..., description="Общее количество непрочитанных")
    conversations: List[ConversationUnreadCount] = Field(..., description="По разговорам")
    
    class Config:
        json_schema_extra = {
            "example": {
                "total_unread": 8,
                "conversations": [
                    {
                        "peer_jid": "alice@localhost",
                        "unread_count": 3,
                        "last_message_timestamp": "2025-09-04T16:30:00Z"
                    },
                    {
                        "peer_jid": "bob@localhost",
                        "unread_count": 5,
                        "last_message_timestamp": "2025-09-04T16:25:00Z"
                    }
                ]
            }
        }


class TypingStatus(BaseModel):
    """Статус набора текста"""
    user_jid: str = Field(..., description="JID пользователя")
    is_typing: bool = Field(..., description="Набирает ли текст")
    timestamp: datetime = Field(..., description="Время статуса")
    
    class Config:
        json_schema_extra = {
            "example": {
                "user_jid": "alice@localhost",
                "is_typing": True,
                "timestamp": "2025-09-04T16:30:00Z"
            }
        }


class TypingIndicator(BaseModel):
    """Индикатор набора текста"""
    peer_jid: str = Field(..., description="JID собеседника")
    is_typing: bool = Field(..., description="Начать/остановить индикацию")
    
    class Config:
        json_schema_extra = {
            "example": {
                "peer_jid": "alice@localhost",
                "is_typing": True
            }
        }
