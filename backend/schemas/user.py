"""
Схемы пользователей
"""
from datetime import datetime
from typing import Optional
from pydantic import BaseModel


class UserCreate(BaseModel):
    """Схема для создания пользователя"""
    username: str
    password: str
    display_name: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None


class UserLogin(BaseModel):
    """Схема для авторизации"""
    username: str
    password: str


class UserProfile(BaseModel):
    """Схема профиля пользователя"""
    username: str
    display_name: Optional[str] = None
    avatar_url: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    status: str
    last_seen: datetime
    
    class Config:
        from_attributes = True


class UserStatusUpdate(BaseModel):
    """Схема для обновления статуса"""
    status: str


class UserProfileUpdate(BaseModel):
    """Схема для обновления профиля"""
    display_name: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None


class UserSearchResult(BaseModel):
    """Результат поиска пользователей"""
    username: str
    display_name: Optional[str] = None
    avatar_url: Optional[str] = None
    status: str
    
    class Config:
        from_attributes = True
