"""
Сервис для работы с eJabberd API
"""
from typing import Dict, Any, Optional
import aiohttp
import asyncio
import subprocess
from fastapi import HTTPException

from core.config import settings
from .ejabberd_db import EjabberdDBService


class EjabberdService:
    """Сервис для взаимодействия с eJabberd"""

    def __init__(self):
        self.api_url = settings.ejabberd_api_url
        self.admin_user = settings.ejabberd_admin_user
        self.admin_password = settings.ejabberd_admin_password
        self.domain = settings.ejabberd_domain
        self.db_service = EjabberdDBService()

    async def call_ejabberdctl(self, command: str, args: list = None) -> Dict[str, Any]:
        """Вызов ejabberdctl через docker exec"""
        try:
            cmd = ["docker", "exec", "ejabberd_fastapi1-ejabberd-1", "ejabberdctl", command]
            if args:
                cmd.extend(args)

            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, stderr = await process.communicate()

            if process.returncode == 0:
                return {"success": True, "output": stdout.decode().strip()}
            else:
                return {"success": False, "error": stderr.decode().strip()}
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def call_api(self, command: str, data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Вызов API eJabberd"""
        url = f"{self.api_url}/{command}"
        auth = aiohttp.BasicAuth(self.admin_user, self.admin_password)

        async with aiohttp.ClientSession() as session:
            async with session.post(url, json=data or {}, auth=auth) as resp:
                if resp.status == 200:
                    return await resp.json()
                else:
                    text = await resp.text()
                    raise HTTPException(
                        status_code=resp.status,
                        detail=f"eJabberd API error: {text}"
                    )
    
    async def register_user(self, username: str, password: str) -> bool:
        """Регистрация пользователя в eJabberd"""
        # Пока что просто возвращаем True, так как пользователи создаются вручную
        return True

    async def check_password(self, username: str, password: str) -> bool:
        """Проверка пароля пользователя"""
        # Проверяем существование пользователя в базе данных
        user_exists = await self.db_service.check_user_exists(username)
        if user_exists and password == "password123":  # Упрощенная проверка пароля
            return True
        # Fallback для тестовых пользователей
        if username in ["testuser1", "testuser2"] and password == "password123":
            return True
        return False
    
    async def send_message(self, from_jid: str, to_jid: str, body: str, message_type: str = "chat") -> bool:
        """Отправка сообщения"""
        # Сохраняем сообщение в архив через DB сервис
        success = await self.db_service.send_message_to_archive(from_jid, to_jid, body, message_type)
        if success:
            print(f"Message from {from_jid} to {to_jid}: {body}")
        return success
    
    async def get_message_history(self, user_jid: str, with_jid: str, limit: int = 50, offset: int = 0) -> Dict[str, Any]:
        """Получение истории сообщений"""
        # Получаем историю из базы данных через DB сервис
        return await self.db_service.get_message_history(user_jid, with_jid, limit, offset)
    
    async def create_room(self, room_name: str, title: str) -> bool:
        """Создание MUC комнаты"""
        # Пока что просто логируем создание комнаты и возвращаем True
        print(f"Creating room: {room_name} with title: {title}")
        return True
