"""
Сервис для работы с LiveKit
"""
from typing import Dict, List
from livekit import api

from core.config import settings


class LiveKitService:
    """Сервис для взаимодействия с LiveKit"""
    
    def __init__(self):
        self.api_key = settings.livekit_api_key
        self.api_secret = settings.livekit_api_secret
        self.url = settings.livekit_url
        self.api = api.LiveKitAPI(
            url=self.url,
            api_key=self.api_key,
            api_secret=self.api_secret
        )
    
    def generate_access_token(self, room_name: str, participant_identity: str, participant_name: str) -> str:
        """Генерация токена доступа для участника"""
        token = api.AccessToken(self.api_key, self.api_secret)\
            .with_identity(participant_identity)\
            .with_name(participant_name)\
            .with_grants(api.VideoGrants(room_join=True, room=room_name))
        
        return token.to_jwt()
    
    def generate_tokens_for_participants(self, room_name: str, participant_jids: List[str]) -> Dict[str, str]:
        """Генерация токенов для всех участников"""
        tokens = {}
        
        for participant_jid in participant_jids:
            username = participant_jid.split('@')[0]
            token = self.generate_access_token(room_name, username, username)
            tokens[participant_jid] = token
        
        return tokens
