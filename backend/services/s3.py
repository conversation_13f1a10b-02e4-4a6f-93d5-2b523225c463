"""
Сервис для работы с S3 (MinIO)
"""
from datetime import timedelta
from typing import Binary<PERSON>
from minio import Minio
from minio.error import S3Error

from core.config import settings


class S3Service:
    """Сервис для работы с S3-совместимым хранилищем"""
    
    def __init__(self):
        self.client = Minio(
            settings.minio_endpoint,
            access_key=settings.minio_access_key,
            secret_key=settings.minio_secret_key,
            secure=False
        )
        self.bucket = settings.minio_bucket
        self._ensure_bucket_exists()
    
    def _ensure_bucket_exists(self):
        """Создание bucket если не существует"""
        try:
            if not self.client.bucket_exists(self.bucket):
                self.client.make_bucket(self.bucket)
        except S3Error as e:
            print(f"MinIO error: {e}")
    
    def upload_file(self, file_data: BinaryIO, s3_key: str, content_type: str, file_size: int) -> bool:
        """Загрузка файла в S3"""
        try:
            self.client.put_object(
                self.bucket,
                s3_key,
                file_data,
                length=file_size,
                content_type=content_type
            )
            return True
        except S3Error:
            return False
    
    def get_presigned_url(self, s3_key: str, expires: timedelta = timedelta(hours=1)) -> str:
        """Получение presigned URL для файла"""
        try:
            return self.client.presigned_get_object(
                self.bucket,
                s3_key,
                expires=expires
            )
        except S3Error:
            return ""
    
    def delete_file(self, s3_key: str) -> bool:
        """Удаление файла из S3"""
        try:
            self.client.remove_object(self.bucket, s3_key)
            return True
        except S3Error:
            return False
