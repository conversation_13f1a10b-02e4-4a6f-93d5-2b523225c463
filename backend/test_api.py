#!/usr/bin/env python3
"""
Простые тесты для API мессенджера
"""

import asyncio
import aiohttp
import json
import sys

BASE_URL = "http://localhost:8000"

async def test_register_user():
    """Тест регистрации пользователя"""
    print("Testing user registration...")
    
    user_data = {
        "username": "testuser",
        "password": "testpass123",
        "display_name": "Test User",
        "email": "<EMAIL>"
    }
    
    async with aiohttp.ClientSession() as session:
        async with session.post(f"{BASE_URL}/api/auth/register", json=user_data) as resp:
            result = await resp.json()
            print(f"Register response: {result}")
            return resp.status == 200

async def test_login_user():
    """Тест авторизации пользователя"""
    print("Testing user login...")
    
    login_data = {
        "username": "testuser",
        "password": "testpass123"
    }
    
    async with aiohttp.ClientSession() as session:
        async with session.post(f"{BASE_URL}/api/auth/login", json=login_data) as resp:
            result = await resp.json()
            print(f"Login response: {result}")
            if resp.status == 200:
                return result.get("access_token")
            return None

async def test_get_profile(token):
    """Тест получения профиля"""
    print("Testing get profile...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    async with aiohttp.ClientSession() as session:
        async with session.get(f"{BASE_URL}/api/users/profile/testuser", headers=headers) as resp:
            result = await resp.json()
            print(f"Profile response: {result}")
            return resp.status == 200

async def test_create_group(token):
    """Тест создания группы"""
    print("Testing group creation...")
    
    headers = {"Authorization": f"Bearer {token}"}
    group_data = {
        "name": "Test Group",
        "description": "A test group",
        "is_public": True,
        "members": []
    }
    
    async with aiohttp.ClientSession() as session:
        async with session.post(f"{BASE_URL}/api/groups/create", json=group_data, headers=headers) as resp:
            result = await resp.json()
            print(f"Group creation response: {result}")
            if resp.status == 200:
                return result.get("group_id")
            return None

async def test_send_message(token):
    """Тест отправки сообщения"""
    print("Testing message sending...")
    
    headers = {"Authorization": f"Bearer {token}"}
    message_data = {
        "to_jid": "admin@localhost",
        "body": "Hello from test!",
        "message_type": "chat"
    }
    
    async with aiohttp.ClientSession() as session:
        async with session.post(f"{BASE_URL}/api/messages/send", json=message_data, headers=headers) as resp:
            result = await resp.json()
            print(f"Send message response: {result}")
            return resp.status == 200

async def test_health_check():
    """Тест доступности API"""
    print("Testing API health...")
    
    async with aiohttp.ClientSession() as session:
        try:
            async with session.get(f"{BASE_URL}/docs") as resp:
                print(f"API docs status: {resp.status}")
                return resp.status == 200
        except Exception as e:
            print(f"Health check failed: {e}")
            return False

async def main():
    """Запуск всех тестов"""
    print("Starting API tests...")
    
    # Проверяем доступность API
    if not await test_health_check():
        print("❌ API is not available")
        sys.exit(1)
    
    print("✅ API is available")
    
    # Тестируем регистрацию
    if await test_register_user():
        print("✅ User registration works")
    else:
        print("❌ User registration failed")
    
    # Тестируем авторизацию
    token = await test_login_user()
    if token:
        print("✅ User login works")
    else:
        print("❌ User login failed")
        return
    
    # Тестируем получение профиля
    if await test_get_profile(token):
        print("✅ Get profile works")
    else:
        print("❌ Get profile failed")
    
    # Тестируем создание группы
    group_id = await test_create_group(token)
    if group_id:
        print("✅ Group creation works")
    else:
        print("❌ Group creation failed")
    
    # Тестируем отправку сообщения
    if await test_send_message(token):
        print("✅ Message sending works")
    else:
        print("❌ Message sending failed")
    
    print("\n🎉 All tests completed!")

if __name__ == "__main__":
    asyncio.run(main())
