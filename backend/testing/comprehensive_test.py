#!/usr/bin/env python3
"""
Комплексный тест всех функций мессенджера
"""

import asyncio
import aiohttp
import json
from typing import Dict, Any

BASE_URL = "http://localhost:8000"

class MessengerTester:
    def __init__(self):
        self.session = None
        self.tokens = {}
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def test_health(self):
        """Тест health check"""
        print("🔍 Testing health check...")
        async with self.session.get(f"{BASE_URL}/api/health") as resp:
            data = await resp.json()
            assert resp.status == 200
            assert data["status"] == "healthy"
            print("✅ Health check passed")
    
    async def test_auth(self):
        """Тест аутентификации"""
        print("🔍 Testing authentication...")
        
        # Регистрация пользователей
        users = [
            {"username": "alice", "password": "password123", "display_name": "Alice <PERSON>", "email": "<EMAIL>"},
            {"username": "bob", "password": "password123", "display_name": "Bob Johnson", "email": "<EMAIL>"}
        ]
        
        for user in users:
            async with self.session.post(f"{BASE_URL}/api/auth/register", json=user) as resp:
                if resp.status == 200:
                    print(f"✅ User {user['username']} registered")
                else:
                    print(f"ℹ️ User {user['username']} already exists")
        
        # Авторизация пользователей
        for user in users:
            login_data = {"username": user["username"], "password": user["password"]}
            async with self.session.post(f"{BASE_URL}/api/auth/login", json=login_data) as resp:
                if resp.status == 200:
                    data = await resp.json()
                    self.tokens[user["username"]] = data["access_token"]
                    print(f"✅ User {user['username']} logged in")
                else:
                    error_data = await resp.json()
                    print(f"❌ Login failed for {user['username']}: {error_data}")
                    raise Exception(f"Login failed for {user['username']}: {error_data}")
    
    async def test_messages(self):
        """Тест сообщений"""
        print("🔍 Testing messages...")
        
        # Отправка сообщения
        headers = {"Authorization": f"Bearer {self.tokens['alice']}"}
        message_data = {
            "to_jid": "bob@localhost",
            "body": "Привет, Bob! Это тестовое сообщение от Alice."
        }
        
        async with self.session.post(f"{BASE_URL}/api/messages/send", json=message_data, headers=headers) as resp:
            data = await resp.json()
            assert resp.status == 200
            assert data["success"] == True
            print("✅ Message sent successfully")
        
        # Получение истории сообщений
        async with self.session.get(f"{BASE_URL}/api/messages/history/bob@localhost?limit=5", headers=headers) as resp:
            data = await resp.json()
            assert resp.status == 200
            assert len(data["messages"]) > 0
            print(f"✅ Message history retrieved: {len(data['messages'])} messages")
    
    async def test_groups(self):
        """Тест групп"""
        print("🔍 Testing groups...")
        
        headers = {"Authorization": f"Bearer {self.tokens['alice']}"}
        
        # Создание группы
        group_data = {
            "name": "Test Group",
            "description": "Группа для тестирования функциональности"
        }
        
        async with self.session.post(f"{BASE_URL}/api/groups/create", json=group_data, headers=headers) as resp:
            data = await resp.json()
            assert resp.status == 200
            group_id = data["group_id"]
            print(f"✅ Group created: {group_id}")
        
        # Получение списка групп
        async with self.session.get(f"{BASE_URL}/api/groups/list", headers=headers) as resp:
            data = await resp.json()
            assert resp.status == 200
            assert len(data) > 0
            print(f"✅ Groups list retrieved: {len(data)} groups")
        
        return group_id
    
    async def test_files(self):
        """Тест файлов"""
        print("🔍 Testing file upload...")
        
        headers = {"Authorization": f"Bearer {self.tokens['alice']}"}
        
        # Создаем тестовый файл
        test_content = "Это тестовый файл для проверки загрузки в мессенджер"
        
        # Загрузка файла
        data = aiohttp.FormData()
        data.add_field('file', test_content, filename='test.txt', content_type='text/plain')
        
        async with self.session.post(f"{BASE_URL}/api/files/upload", data=data, headers=headers) as resp:
            result = await resp.json()
            assert resp.status == 200
            file_id = result["file_id"]
            print(f"✅ File uploaded: {file_id}")
        
        # Получение файла
        async with self.session.get(f"{BASE_URL}/api/files/{file_id}", headers=headers) as resp:
            data = await resp.json()
            assert resp.status == 200
            assert "url" in data
            print("✅ File URL retrieved")
        
        return file_id
    
    async def test_calls(self):
        """Тест звонков"""
        print("🔍 Testing calls...")
        
        headers = {"Authorization": f"Bearer {self.tokens['alice']}"}
        
        # Создание звонка
        call_data = {
            "call_type": "video",
            "participants": ["bob@localhost"]
        }
        
        async with self.session.post(f"{BASE_URL}/api/calls/create", json=call_data, headers=headers) as resp:
            data = await resp.json()
            assert resp.status == 200
            call_id = data["call_id"]
            assert "tokens" in data
            print(f"✅ Call created: {call_id}")
        
        return call_id
    
    async def test_users(self):
        """Тест пользователей"""
        print("🔍 Testing users...")
        
        headers = {"Authorization": f"Bearer {self.tokens['alice']}"}
        
        # Поиск пользователей
        async with self.session.get(f"{BASE_URL}/api/users/search?q=bob", headers=headers) as resp:
            data = await resp.json()
            assert resp.status == 200
            assert len(data) > 0
            print(f"✅ User search: found {len(data)} users")
        
        # Онлайн пользователи
        async with self.session.get(f"{BASE_URL}/api/users/online", headers=headers) as resp:
            data = await resp.json()
            assert resp.status == 200
            print(f"✅ Online users: {len(data['online_users'])} users online")
    
    async def test_notifications(self):
        """Тест уведомлений"""
        print("🔍 Testing notifications...")
        
        headers = {"Authorization": f"Bearer {self.tokens['bob']}"}
        
        # Количество непрочитанных уведомлений
        async with self.session.get(f"{BASE_URL}/api/notifications/unread-count", headers=headers) as resp:
            data = await resp.json()
            assert resp.status == 200
            print(f"✅ Unread notifications: {data['unread_count']}")
    
    async def run_all_tests(self):
        """Запуск всех тестов"""
        print("🚀 Starting comprehensive messenger tests...\n")
        
        try:
            await self.test_health()
            await self.test_auth()
            await self.test_messages()
            group_id = await self.test_groups()
            file_id = await self.test_files()
            call_id = await self.test_calls()
            await self.test_users()
            await self.test_notifications()
            
            print("\n🎉 All tests passed successfully!")
            print(f"📊 Test results:")
            print(f"   - Group created: {group_id}")
            print(f"   - File uploaded: {file_id}")
            print(f"   - Call created: {call_id}")
            
        except Exception as e:
            print(f"\n❌ Test failed: {e}")
            raise

async def main():
    async with MessengerTester() as tester:
        await tester.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
