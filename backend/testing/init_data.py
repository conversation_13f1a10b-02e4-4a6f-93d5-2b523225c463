#!/usr/bin/env python3
"""
Скрипт для инициализации тестовых данных
"""

import asyncio
import asyncpg
import aiohttp
import os
from dotenv import load_dotenv

load_dotenv()

DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://postgres:postgres123@localhost:5432/messenger")
EJABBERD_API_URL = os.getenv("EJABBERD_API_URL", "http://localhost:5280/api")
EJABBERD_ADMIN_USER = os.getenv("EJABBERD_ADMIN_USER", "admin")
EJABBERD_ADMIN_PASSWORD = os.getenv("EJABBERD_ADMIN_PASSWORD", "admin123")

async def call_ejabberd_api(command: str, data: dict = None):
    """Вызов API eJabberd"""
    url = f"{EJABBERD_API_URL}/{command}"
    auth = aiohttp.BasicAuth(EJABBERD_ADMIN_USER, EJABBERD_ADMIN_PASSWORD)
    
    async with aiohttp.ClientSession() as session:
        async with session.post(url, json=data or {}, auth=auth) as resp:
            if resp.status == 200:
                return await resp.json()
            else:
                text = await resp.text()
                print(f"eJabberd API error: {text}")
                return None

async def create_test_users():
    """Создание тестовых пользователей"""
    print("Creating test users...")
    
    test_users = [
        {"username": "alice", "password": "alice123", "display_name": "Alice Smith"},
        {"username": "bob", "password": "bob123", "display_name": "Bob Johnson"},
        {"username": "charlie", "password": "charlie123", "display_name": "Charlie Brown"},
    ]
    
    # Подключаемся к БД
    conn = await asyncpg.connect(DATABASE_URL)
    
    try:
        for user in test_users:
            print(f"Creating user: {user['username']}")
            
            # Создаем пользователя в eJabberd
            result = await call_ejabberd_api("register", {
                "user": user["username"],
                "host": "localhost",
                "password": user["password"]
            })
            
            if result is not None:
                # Сохраняем дополнительную информацию в нашей БД
                await conn.execute("""
                    INSERT INTO users_extended (username, display_name)
                    VALUES ($1, $2)
                    ON CONFLICT (username, server) DO UPDATE SET
                    display_name = $2
                """, user["username"], user["display_name"])
                
                print(f"✅ User {user['username']} created successfully")
            else:
                print(f"❌ Failed to create user {user['username']}")
    
    finally:
        await conn.close()

async def create_test_group():
    """Создание тестовой группы"""
    print("Creating test group...")
    
    conn = await asyncpg.connect(DATABASE_URL)
    
    try:
        room_id = "testgroup"
        room_jid = f"{room_id}@conference.localhost"
        
        # Создаем комнату в eJabberd
        result = await call_ejabberd_api("create_room", {
            "name": room_id,
            "service": "conference.localhost",
            "host": "localhost"
        })
        
        if result is not None:
            # Устанавливаем название комнаты
            await call_ejabberd_api("change_room_option", {
                "name": room_id,
                "service": "conference.localhost",
                "option": "title",
                "value": "Test Group Chat"
            })
            
            # Сохраняем в БД
            group_record = await conn.fetchrow("""
                INSERT INTO group_chats (jid, name, description, created_by, is_public)
                VALUES ($1, $2, $3, $4, $5)
                RETURNING id
                ON CONFLICT (jid) DO UPDATE SET
                name = $2, description = $3
                RETURNING id
            """, room_jid, "Test Group Chat", "A test group for demo", "admin@localhost", True)
            
            # Добавляем участников
            test_members = ["alice@localhost", "bob@localhost", "charlie@localhost"]
            
            for member_jid in test_members:
                await conn.execute("""
                    INSERT INTO group_members (group_id, user_jid)
                    VALUES ($1, $2)
                    ON CONFLICT (group_id, user_jid) DO NOTHING
                """, group_record["id"], member_jid)
            
            print("✅ Test group created successfully")
        else:
            print("❌ Failed to create test group")
    
    finally:
        await conn.close()

async def main():
    """Основная функция"""
    print("Initializing test data...")
    
    try:
        await create_test_users()
        await create_test_group()
        print("\n🎉 Test data initialization completed!")
        
        print("\nTest users created:")
        print("- alice / alice123")
        print("- bob / bob123") 
        print("- charlie / charlie123")
        print("\nTest group: <EMAIL>")
        
    except Exception as e:
        print(f"❌ Error during initialization: {e}")

if __name__ == "__main__":
    asyncio.run(main())
