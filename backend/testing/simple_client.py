#!/usr/bin/env python3
"""
Упрощенный консольный клиент для тестирования
"""
import asyncio
import aiohttp
import json

class SimpleClient:
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.access_token = None
        self.user_jid = None
    
    async def login(self, username, password):
        """Авторизация"""
        async with aiohttp.ClientSession() as session:
            data = {"username": username, "password": password}
            async with session.post(f"{self.base_url}/api/auth/login", json=data) as resp:
                if resp.status == 200:
                    result = await resp.json()
                    self.access_token = result["access_token"]
                    self.user_jid = result["user_jid"]
                    print(f"✅ Авторизован как {self.user_jid}")
                    return True
                else:
                    result = await resp.json()
                    print(f"❌ Ошибка авторизации: {result}")
                    return False
    
    def _get_headers(self):
        return {"Authorization": f"Bearer {self.access_token}"}
    
    async def send_message(self, to_jid, body):
        """Отправка сообщения"""
        async with aiohttp.ClientSession() as session:
            data = {"to_jid": to_jid, "body": body, "message_type": "chat"}
            async with session.post(f"{self.base_url}/api/messages/send", json=data, headers=self._get_headers()) as resp:
                if resp.status == 200:
                    print(f"✅ Сообщение отправлено: {body}")
                    return True
                else:
                    result = await resp.json()
                    print(f"❌ Ошибка отправки: {result}")
                    return False
    
    async def get_history(self, jid, limit=5):
        """Получение истории"""
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{self.base_url}/api/messages/history/{jid}?limit={limit}", headers=self._get_headers()) as resp:
                if resp.status == 200:
                    result = await resp.json()
                    messages = result.get("messages", [])
                    print(f"\n📜 История с {jid} ({len(messages)} сообщений):")
                    for msg in messages[-limit:]:
                        print(f"  {msg.get('from', 'N/A')}: {msg.get('body', 'N/A')}")
                    return True
                else:
                    result = await resp.json()
                    print(f"❌ Ошибка получения истории: {result}")
                    return False
    
    async def search_users(self, query):
        """Поиск пользователей"""
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{self.base_url}/api/users/search?q={query}", headers=self._get_headers()) as resp:
                if resp.status == 200:
                    users = await resp.json()
                    print(f"\n🔍 Найдено пользователей: {len(users)}")
                    for user in users:
                        print(f"  - {user.get('username', 'N/A')} ({user.get('display_name', 'N/A')})")
                    return users
                else:
                    result = await resp.json()
                    print(f"❌ Ошибка поиска: {result}")
                    return []

async def demo():
    """Демонстрация работы клиента"""
    client = SimpleClient()
    
    print("🚀 Простой консольный клиент мессенджера")
    
    # Авторизация
    success = await client.login("testuser1", "password123")
    if not success:
        return
    
    # Отправка сообщения
    await client.send_message("alice@localhost", "Привет, Alice! Это сообщение из простого клиента.")
    
    # Получение истории
    await client.get_history("alice@localhost")
    
    # Поиск пользователей
    await client.search_users("alice")
    
    print("\n✅ Демонстрация завершена!")

if __name__ == "__main__":
    asyncio.run(demo())
