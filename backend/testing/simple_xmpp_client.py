#!/usr/bin/env python3
"""
Простой XMPP клиент для тестирования подключения к eJabberd
"""

import asyncio
import aiohttp
import json
from typing import Optional

class SimpleXMPPClient:
    def __init__(self, username: str, password: str, domain: str = "localhost"):
        self.username = username
        self.password = password
        self.domain = domain
        self.jid = f"{username}@{domain}"
        self.token: Optional[str] = None
        
    async def authenticate(self):
        """Аутентификация через FastAPI"""
        async with aiohttp.ClientSession() as session:
            login_data = {
                "username": self.username,
                "password": self.password
            }
            
            async with session.post("http://localhost:8000/api/auth/login", json=login_data) as resp:
                if resp.status == 200:
                    data = await resp.json()
                    self.token = data["access_token"]
                    print(f"✅ Authenticated as {self.jid}")
                    return True
                else:
                    error = await resp.json()
                    print(f"❌ Authentication failed: {error}")
                    return False
    
    async def send_message(self, to_jid: str, message: str):
        """Отправка сообщения через FastAPI"""
        if not self.token:
            print("❌ Not authenticated")
            return False
            
        async with aiohttp.ClientSession() as session:
            headers = {"Authorization": f"Bearer {self.token}"}
            message_data = {
                "to_jid": to_jid,
                "body": message
            }
            
            async with session.post("http://localhost:8000/api/messages/send", 
                                  json=message_data, headers=headers) as resp:
                if resp.status == 200:
                    print(f"✅ Message sent to {to_jid}: {message}")
                    return True
                else:
                    error = await resp.json()
                    print(f"❌ Failed to send message: {error}")
                    return False
    
    async def get_message_history(self, with_jid: str, limit: int = 10):
        """Получение истории сообщений"""
        if not self.token:
            print("❌ Not authenticated")
            return []
            
        async with aiohttp.ClientSession() as session:
            headers = {"Authorization": f"Bearer {self.token}"}
            
            async with session.get(f"http://localhost:8000/api/messages/history/{with_jid}?limit={limit}", 
                                 headers=headers) as resp:
                if resp.status == 200:
                    data = await resp.json()
                    messages = data["messages"]
                    print(f"📜 Message history with {with_jid} ({len(messages)} messages):")
                    for msg in messages:
                        print(f"  [{msg['timestamp']}] {msg['from']}: {msg['body']}")
                    return messages
                else:
                    error = await resp.json()
                    print(f"❌ Failed to get history: {error}")
                    return []
    
    async def get_online_users(self):
        """Получение списка онлайн пользователей"""
        if not self.token:
            print("❌ Not authenticated")
            return []
            
        async with aiohttp.ClientSession() as session:
            headers = {"Authorization": f"Bearer {self.token}"}
            
            async with session.get("http://localhost:8000/api/users/online", headers=headers) as resp:
                if resp.status == 200:
                    data = await resp.json()
                    users = data["online_users"]
                    print(f"👥 Online users ({len(users)}):")
                    for user in users:
                        print(f"  - {user['user_jid']} ({user['status']})")
                    return users
                else:
                    error = await resp.json()
                    print(f"❌ Failed to get online users: {error}")
                    return []

async def demo_conversation():
    """Демонстрация разговора между двумя пользователями"""
    print("🚀 Starting XMPP client demo...\n")
    
    # Создаем двух клиентов
    alice = SimpleXMPPClient("alice", "password123")
    bob = SimpleXMPPClient("bob", "password123")
    
    # Аутентификация
    if not await alice.authenticate():
        return
    if not await bob.authenticate():
        return
    
    print()
    
    # Alice отправляет сообщение Bob'у
    await alice.send_message("bob@localhost", "Привет, Bob! Как дела?")
    await asyncio.sleep(1)
    
    # Bob отвечает Alice
    await bob.send_message("alice@localhost", "Привет, Alice! Все отлично, спасибо!")
    await asyncio.sleep(1)
    
    # Alice отправляет еще одно сообщение
    await alice.send_message("bob@localhost", "Отлично! Хочешь пойти на кофе?")
    await asyncio.sleep(1)
    
    print()
    
    # Bob проверяет историю сообщений с Alice
    await bob.get_message_history("alice@localhost", 5)
    
    print()
    
    # Проверяем онлайн пользователей
    await alice.get_online_users()
    
    print("\n✅ Demo completed!")

async def interactive_client():
    """Интерактивный клиент"""
    print("🚀 Interactive XMPP Client")
    print("Commands:")
    print("  /login <username> <password> - Login")
    print("  /send <jid> <message> - Send message")
    print("  /history <jid> [limit] - Get message history")
    print("  /online - Get online users")
    print("  /quit - Exit")
    print()
    
    client = None
    
    while True:
        try:
            command = input("> ").strip()
            
            if command.startswith("/login"):
                parts = command.split(" ", 2)
                if len(parts) >= 3:
                    username, password = parts[1], parts[2]
                    client = SimpleXMPPClient(username, password)
                    await client.authenticate()
                else:
                    print("Usage: /login <username> <password>")
            
            elif command.startswith("/send"):
                if not client:
                    print("❌ Please login first")
                    continue
                parts = command.split(" ", 2)
                if len(parts) >= 3:
                    jid, message = parts[1], parts[2]
                    await client.send_message(jid, message)
                else:
                    print("Usage: /send <jid> <message>")
            
            elif command.startswith("/history"):
                if not client:
                    print("❌ Please login first")
                    continue
                parts = command.split(" ")
                if len(parts) >= 2:
                    jid = parts[1]
                    limit = int(parts[2]) if len(parts) > 2 else 10
                    await client.get_message_history(jid, limit)
                else:
                    print("Usage: /history <jid> [limit]")
            
            elif command == "/online":
                if not client:
                    print("❌ Please login first")
                    continue
                await client.get_online_users()
            
            elif command == "/quit":
                break
            
            else:
                print("Unknown command. Type /quit to exit.")
        
        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"❌ Error: {e}")
    
    print("👋 Goodbye!")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "interactive":
        asyncio.run(interactive_client())
    else:
        asyncio.run(demo_conversation())
