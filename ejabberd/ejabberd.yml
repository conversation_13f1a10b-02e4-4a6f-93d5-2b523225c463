hosts:
  - localhost

loglevel: info

certfiles:
  - /opt/ejabberd/conf/server.pem

listen:
  # XMPP клиентские соединения
  -
    port: 5222
    ip: "::"
    module: ejabberd_c2s
    max_stanza_size: 262144
    shaper: c2s_shaper
    access: c2s
    starttls_required: false
  
  # XMPP серверные соединения
  -
    port: 5269
    ip: "::"
    module: ejabberd_s2s_in
    max_stanza_size: 524288
  
  # HTTP API и админка
  -
    port: 5280
    ip: "::"
    module: ejabberd_http
    request_handlers:
      /api: mod_http_api
      /admin: ejabberd_web_admin
      /upload: mod_http_upload
    web_admin: true

# Подключение к PostgreSQL
sql_type: pgsql
sql_server: postgres
sql_database: messenger
sql_username: postgres
sql_password: postgres123
sql_port: 5432

# Аутентификация через базу данных
auth_method: sql
auth_password_format: scram

# Основные модули
modules:
  # Ростер (список контактов)
  mod_roster:
    versioning: true
    store_current_id: true

  # Приватное хранилище
  mod_private: {}

  # vCard (профили пользователей)
  mod_vcard:
    search: true

  # Последняя активность
  mod_last: {}

  # Офлайн сообщения
  mod_offline:
    access_max_user_messages: max_user_offline_messages

  # История сообщений (MAM)
  mod_mam:
    default: always
    compress_xml: true
    db_type: sql
    
  # Групповые чаты (MUC)
  mod_muc:
    access:
      - allow
    access_admin:
      - allow: admin
    access_create: muc_create
    access_persistent: muc_create
    default_room_options:
      persistent: true
      public_list: true
      
  # Загрузка файлов
  mod_http_upload:
    put_url: "http://localhost:5280/upload"
    thumbnail: false
    max_size: 104857600 # 100MB

  # HTTP API
  mod_http_api:
    admin_ip_access: all

  # Ping
  mod_ping: {}

  # Регистрация пользователей
  mod_register:
    access: register
    welcome_message:
      subject: "Добро пожаловать!"
      body: "Добро пожаловать в наш мессенджер!"

  # Push уведомления
  mod_push: {}

# Права доступа
acl:
  admin:
    user:
      - admin: localhost
  local:
    user_regexp: ""

access_rules:
  local:
    - allow: local
  c2s:
    - deny: blocked
    - allow
  announce:
    - allow: admin
  configure:
    - allow: admin
  muc_create:
    - allow: local
  register:
    - allow
  api_permissions:
    - allow: admin
  
# Шейперы (ограничение трафика)
shaper:
  normal: 1000
  fast: 50000

shaper_rules:
  max_user_sessions: 10
  max_user_offline_messages:
    - 5000: admin
    - 100
  c2s_shaper:
    - none: admin
    - normal
  s2s_shaper: fast

# API permissions
api_permissions:
  "admin access":
    from:
      - ejabberd_ctl
      - mod_http_api
    who: all
    what: "*"