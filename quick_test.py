#!/usr/bin/env python3
import requests

# Тест API
try:
    # Проверка здоровья
    resp = requests.get("http://localhost:8000/api/health")
    print(f"Health check: {resp.status_code} - {resp.json()}")
    
    # Авторизация
    login_data = {"username": "testuser1", "password": "password123"}
    resp = requests.post("http://localhost:8000/api/auth/login", json=login_data)
    if resp.status_code == 200:
        token = resp.json()["access_token"]
        print(f"Login successful, token: {token[:20]}...")
        
        # Онлайн пользователи
        headers = {"Authorization": f"Bearer {token}"}
        resp = requests.get("http://localhost:8000/api/users/online", headers=headers)
        if resp.status_code == 200:
            online_users = resp.json()
            print(f"Online users: {online_users}")
        else:
            print(f"Error getting online users: {resp.status_code}")
    else:
        print(f"Login failed: {resp.status_code}")

except Exception as e:
    print(f"Error: {e}")
