#!/bin/bash

echo "🚀 Тестирование API мессенджера"

# 1. Проверка здоровья
echo -e "\n1. Проверка здоровья API..."
curl -s http://localhost:8000/api/health

# 2. Авторизация testuser1
echo -e "\n2. Авторизация testuser1..."
RESPONSE=$(curl -s -X POST http://localhost:8000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "testuser1", "password": "password123"}')
echo $RESPONSE
TOKEN=$(echo $RESPONSE | grep -o '"access_token":"[^"]*"' | cut -d'"' -f4)

if [ ! -z "$TOKEN" ]; then
  echo "✅ Авторизация успешна"

  # 3. Онлайн пользователи
  echo -e "\n3. Онлайн пользователи..."
  curl -s -X GET "http://localhost:8000/api/users/online" \
    -H "Authorization: Bearer $TOKEN"

  # 4. Отправка сообщения
  echo -e "\n4. Отправка сообщения testuser2..."
  curl -s -X POST http://localhost:8000/api/messages/send \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $TOKEN" \
    -d '{"to_jid": "testuser2@localhost", "body": "Тестовое сообщение из скрипта", "message_type": "chat"}'

  # 5. Поиск пользователей
  echo -e "\n5. Поиск пользователей..."
  curl -s -X GET "http://localhost:8000/api/users/search?q=test" \
    -H "Authorization: Bearer $TOKEN"

  # 6. Авторизация testuser2 и проверка уведомлений
  echo -e "\n6. Проверка уведомлений testuser2..."
  RESPONSE2=$(curl -s -X POST http://localhost:8000/api/auth/login \
    -H "Content-Type: application/json" \
    -d '{"username": "testuser2", "password": "password123"}')
  TOKEN2=$(echo $RESPONSE2 | grep -o '"access_token":"[^"]*"' | cut -d'"' -f4)

  if [ ! -z "$TOKEN2" ]; then
    curl -s -X GET "http://localhost:8000/api/notifications/unread-count" \
      -H "Authorization: Bearer $TOKEN2"
  fi
  
else
  echo "❌ Ошибка авторизации"
fi

echo -e "\n🎉 Тестирование завершено!"
