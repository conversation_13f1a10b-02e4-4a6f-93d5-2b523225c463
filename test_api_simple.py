#!/usr/bin/env python3
"""
Простой тест API мессенджера
"""
import asyncio
import aiohttp
import json

async def test_api():
    """Тестирование основных функций API"""
    base_url = "http://localhost:8000"
    
    print("🚀 Тестирование API мессенджера")
    
    # 1. Проверка здоровья API
    print("\n1. Проверка здоровья API...")
    async with aiohttp.ClientSession() as session:
        async with session.get(f"{base_url}/api/health") as resp:
            if resp.status == 200:
                result = await resp.json()
                print(f"✅ API здоров: {result}")
            else:
                print(f"❌ API недоступен: {resp.status}")
                return
    
    # 2. Авторизация
    print("\n2. Авторизация testuser1...")
    async with aiohttp.ClientSession() as session:
        login_data = {"username": "testuser1", "password": "password123"}
        async with session.post(f"{base_url}/api/auth/login", json=login_data) as resp:
            if resp.status == 200:
                auth_result = await resp.json()
                access_token = auth_result["access_token"]
                user_jid = auth_result["user_jid"]
                print(f"✅ Авторизация успешна: {user_jid}")
            else:
                result = await resp.json()
                print(f"❌ Ошибка авторизации: {result}")
                return
    
    headers = {"Authorization": f"Bearer {access_token}"}
    
    # 3. Отправка сообщения
    print("\n3. Отправка сообщения...")
    async with aiohttp.ClientSession() as session:
        message_data = {
            "to_jid": "testuser2@localhost",
            "body": "Тестовое сообщение из API теста",
            "message_type": "chat"
        }
        async with session.post(f"{base_url}/api/messages/send", json=message_data, headers=headers) as resp:
            if resp.status == 200:
                result = await resp.json()
                print(f"✅ Сообщение отправлено: {result}")
            else:
                result = await resp.json()
                print(f"❌ Ошибка отправки: {result}")
    
    # 4. Получение истории сообщений
    print("\n4. Получение истории сообщений...")
    async with aiohttp.ClientSession() as session:
        async with session.get(f"{base_url}/api/messages/history/testuser2@localhost?limit=5", headers=headers) as resp:
            if resp.status == 200:
                result = await resp.json()
                messages = result.get("messages", [])
                print(f"✅ Получено {len(messages)} сообщений")
                for msg in messages[-3:]:  # Показываем последние 3
                    print(f"  - {msg.get('from', 'N/A')}: {msg.get('body', 'N/A')}")
            else:
                result = await resp.json()
                print(f"❌ Ошибка получения истории: {result}")
    
    # 5. Создание группы
    print("\n5. Создание группы...")
    async with aiohttp.ClientSession() as session:
        group_data = {
            "name": "Тестовая группа",
            "description": "Группа для тестирования API",
            "is_public": True,
            "members": []
        }
        async with session.post(f"{base_url}/api/groups/create", json=group_data, headers=headers) as resp:
            if resp.status == 200:
                result = await resp.json()
                group_jid = result["jid"]
                group_id = result["group_id"]
                print(f"✅ Группа создана: {group_jid} (ID: {group_id})")
            else:
                result = await resp.json()
                print(f"❌ Ошибка создания группы: {result}")
                group_id = None
    
    # 6. Список групп
    print("\n6. Список групп...")
    async with aiohttp.ClientSession() as session:
        async with session.get(f"{base_url}/api/groups/list", headers=headers) as resp:
            if resp.status == 200:
                groups = await resp.json()
                print(f"✅ Найдено {len(groups)} групп")
                for group in groups:
                    print(f"  - {group.get('name', 'N/A')} ({group.get('role', 'N/A')})")
            else:
                result = await resp.json()
                print(f"❌ Ошибка получения групп: {result}")
    
    # 7. Поиск пользователей
    print("\n7. Поиск пользователей...")
    async with aiohttp.ClientSession() as session:
        async with session.get(f"{base_url}/api/users/search?q=alice", headers=headers) as resp:
            if resp.status == 200:
                users = await resp.json()
                print(f"✅ Найдено {len(users)} пользователей")
                for user in users:
                    print(f"  - {user.get('username', 'N/A')} ({user.get('display_name', 'N/A')})")
            else:
                result = await resp.json()
                print(f"❌ Ошибка поиска: {result}")

    # 8. Приглашение в группу (если группа была создана)
    if group_id:
        print(f"\n8. Приглашение alice в группу {group_id}...")
        async with aiohttp.ClientSession() as session:
            invite_data = {"user_jids": ["alice@localhost"]}
            async with session.post(f"{base_url}/api/groups/{group_id}/invite", json=invite_data, headers=headers) as resp:
                if resp.status == 200:
                    result = await resp.json()
                    print(f"✅ Приглашение успешно: {result}")
                else:
                    result = await resp.json()
                    print(f"❌ Ошибка приглашения: {result}")

    # 9. Проверка количества непрочитанных уведомлений
    print(f"\n9. Проверка уведомлений для testuser2...")
    async with aiohttp.ClientSession() as session:
        # Авторизуемся как testuser2
        login_data = {"username": "testuser2", "password": "password123"}
        async with session.post(f"{base_url}/api/auth/login", json=login_data) as resp:
            if resp.status == 200:
                auth_result = await resp.json()
                testuser2_token = auth_result["access_token"]

                # Проверяем количество уведомлений
                headers2 = {"Authorization": f"Bearer {testuser2_token}"}
                async with session.get(f"{base_url}/api/notifications/unread-count", headers=headers2) as resp:
                    if resp.status == 200:
                        result = await resp.json()
                        print(f"✅ Непрочитанных уведомлений у testuser2: {result['unread_count']}")
                    else:
                        result = await resp.json()
                        print(f"❌ Ошибка получения уведомлений: {result}")
            else:
                print("❌ Не удалось авторизоваться как testuser2")

    # 10. Проверка онлайн пользователей
    print(f"\n10. Проверка онлайн пользователей...")
    async with aiohttp.ClientSession() as session:
        async with session.get(f"{base_url}/api/users/online", headers=headers) as resp:
            if resp.status == 200:
                online_users = await resp.json()
                print(f"✅ Онлайн пользователей: {len(online_users)}")
                for user_jid, status in online_users.items():
                    print(f"  - {user_jid}: {status}")
            else:
                result = await resp.json()
                print(f"❌ Ошибка получения онлайн пользователей: {result}")

    print("\n🎉 Тестирование завершено!")

if __name__ == "__main__":
    asyncio.run(test_api())
