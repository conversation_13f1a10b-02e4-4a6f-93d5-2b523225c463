#!/bin/bash

echo "🚀 Тестирование современных функций мессенджера"

# Проверка API
echo -e "\n1. Проверка API..."
if curl -s --max-time 5 http://localhost:8000/api/health > /dev/null; then
    echo "✅ API работает"
else
    echo "❌ API недоступен"
    exit 1
fi

# Авторизация
echo -e "\n2. Авторизация..."
TOKEN=$(curl -s --max-time 5 -X POST http://localhost:8000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "testuser1", "password": "password123"}' | \
  python3 -c "import sys, json; print(json.load(sys.stdin).get('access_token', ''))" 2>/dev/null)

if [ -n "$TOKEN" ] && [ "$TOKEN" != "" ]; then
    echo "✅ Авторизация успешна"
else
    echo "❌ Ошибка авторизации"
    exit 1
fi

# Создание канала
echo -e "\n3. Создание канала..."
CHANNEL_RESPONSE=$(curl -s --max-time 5 -X POST http://localhost:8000/api/channels/create \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "name": "Тестовый канал",
    "username": "test_channel_modern",
    "description": "Канал для тестирования",
    "is_public": true
  }')

if echo "$CHANNEL_RESPONSE" | grep -q '"id"'; then
    CHANNEL_ID=$(echo "$CHANNEL_RESPONSE" | python3 -c "import sys, json; print(json.load(sys.stdin).get('id', ''))" 2>/dev/null)
    echo "✅ Канал создан (ID: $CHANNEL_ID)"
else
    echo "❌ Ошибка создания канала"
    echo "$CHANNEL_RESPONSE"
fi

# Поиск каналов
echo -e "\n4. Поиск каналов..."
SEARCH_RESPONSE=$(curl -s --max-time 5 -X GET "http://localhost:8000/api/channels/search?q=тест" \
  -H "Authorization: Bearer $TOKEN")

if echo "$SEARCH_RESPONSE" | grep -q '\['; then
    CHANNELS_COUNT=$(echo "$SEARCH_RESPONSE" | python3 -c "import sys, json; print(len(json.load(sys.stdin)))" 2>/dev/null)
    echo "✅ Найдено каналов: $CHANNELS_COUNT"
else
    echo "❌ Ошибка поиска каналов"
fi

# Добавление реакции
echo -e "\n5. Добавление реакции..."
REACTION_RESPONSE=$(curl -s --max-time 5 -X POST http://localhost:8000/api/reactions/add \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "message_id": "test_message_123",
    "emoji": "👍"
  }')

if echo "$REACTION_RESPONSE" | grep -q '"id"'; then
    echo "✅ Реакция добавлена"
else
    echo "❌ Ошибка добавления реакции"
    echo "$REACTION_RESPONSE"
fi

# Получение реакций
echo -e "\n6. Получение реакций..."
REACTIONS_RESPONSE=$(curl -s --max-time 5 -X GET "http://localhost:8000/api/reactions/message/test_message_123" \
  -H "Authorization: Bearer $TOKEN")

if echo "$REACTIONS_RESPONSE" | grep -q '"total_count"'; then
    TOTAL_REACTIONS=$(echo "$REACTIONS_RESPONSE" | python3 -c "import sys, json; print(json.load(sys.stdin).get('total_count', 0))" 2>/dev/null)
    echo "✅ Реакций на сообщение: $TOTAL_REACTIONS"
else
    echo "❌ Ошибка получения реакций"
fi

# Индикатор печати
echo -e "\n7. Индикатор печати..."
TYPING_RESPONSE=$(curl -s --max-time 5 -X POST http://localhost:8000/api/messages/typing \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "to_jid": "testuser2@localhost",
    "is_typing": true
  }')

if echo "$TYPING_RESPONSE" | grep -q '"success"'; then
    echo "✅ Индикатор печати установлен"
else
    echo "❌ Ошибка установки индикатора печати"
fi

# Популярные реакции
echo -e "\n8. Популярные реакции..."
POPULAR_RESPONSE=$(curl -s --max-time 5 -X GET "http://localhost:8000/api/reactions/popular" \
  -H "Authorization: Bearer $TOKEN")

if echo "$POPULAR_RESPONSE" | grep -q '\['; then
    echo "✅ Получены популярные реакции"
else
    echo "❌ Ошибка получения популярных реакций"
fi

echo -e "\n🎉 Тестирование современных функций завершено!"
